// Offline detection and network utilities
export class OfflineManager {
  constructor() {
    this.isOnline = navigator.onLine;
    this.listeners = [];
    this.init();
  }

  init() {
    window.addEventListener('online', this.handleOnline.bind(this));
    window.addEventListener('offline', this.handleOffline.bind(this));
  }

  handleOnline() {
    console.log('Network connection restored');
    this.isOnline = true;
    this.notifyListeners('online');
  }

  handleOffline() {
    console.log('Network connection lost');
    this.isOnline = false;
    this.notifyListeners('offline');
  }

  addListener(callback) {
    this.listeners.push(callback);
  }

  removeListener(callback) {
    this.listeners = this.listeners.filter(listener => listener !== callback);
  }

  notifyListeners(status) {
    this.listeners.forEach(callback => callback(status));
  }

  getStatus() {
    return this.isOnline;
  }

  // Check actual connectivity by making a small request
  async checkConnectivity() {
    if (!navigator.onLine) {
      return false;
    }

    try {
      // Try to fetch a small resource from the server
      const response = await fetch('http://197.140.142.170/api/auth/profile/', {
        method: 'HEAD',
        cache: 'no-cache',
        headers: {
          'Authorization': `Token ${localStorage.getItem('authToken') || ''}`
        }
      });
      return response.ok;
    } catch (error) {
      console.log('Connectivity check failed:', error);
      return false;
    }
  }
}

// Create singleton instance
export const offlineManager = new OfflineManager();

// Utility functions
export const isOnline = () => offlineManager.getStatus();

export const waitForOnline = () => {
  return new Promise((resolve) => {
    if (isOnline()) {
      resolve();
    } else {
      const checkOnline = () => {
        if (isOnline()) {
          offlineManager.removeListener(checkOnline);
          resolve();
        }
      };
      offlineManager.addListener(checkOnline);
    }
  });
};

export const onNetworkChange = (callback) => {
  offlineManager.addListener(callback);
  return () => offlineManager.removeListener(callback);
};
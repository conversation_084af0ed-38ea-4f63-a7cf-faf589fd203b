const CACHE_NAME = 'e-inspection-v1';
const STATIC_CACHE = 'e-inspection-static-v1';
const DYNAMIC_CACHE = 'e-inspection-dynamic-v1';

// Files to cache immediately
const STATIC_FILES = [
  '/',
  '/offline.html',
  '/manifest.json',
  '/favicon.ico',
  '/logo192.png',
  '/logo512.png',
  '/static/js/main.js',
  '/static/css/main.css'
];

// API endpoints to cache
const API_ENDPOINTS = [
  '/api/inspections/inspections/',
  '/api/inspections/userfiles/',
  '/api/auth/profile/'
];

self.addEventListener('install', (event) => {
  console.log('Service Worker installing.');
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('Caching static files');
        return cache.addAll(STATIC_FILES);
      })
      .catch((error) => {
        console.error('Error caching static files:', error);
      })
  );
  self.skipWaiting();
});

self.addEventListener('activate', (event) => {
  console.log('Service Worker activating.');
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
            console.log('Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
  self.clients.claim();
});

self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Handle API requests
  if (url.origin === 'http://***************') {
    event.respondWith(
      fetch(request)
        .then((response) => {
          // Cache successful GET requests
          if (request.method === 'GET' && response.status === 200) {
            const responseClone = response.clone();
            caches.open(DYNAMIC_CACHE)
              .then((cache) => {
                cache.put(request, responseClone);
              });
          }
          return response;
        })
        .catch(() => {
          // Return cached version if available
          return caches.match(request)
            .then((cachedResponse) => {
              if (cachedResponse) {
                return cachedResponse;
              }
              // Return offline page or error
              return new Response(JSON.stringify({ error: 'Offline' }), {
                status: 503,
                headers: { 'Content-Type': 'application/json' }
              });
            });
        })
    );
  } else {
    // Handle static assets and navigation requests
    if (request.mode === 'navigate') {
      // Handle navigation requests (SPA routes)
      event.respondWith(
        caches.match(request)
          .then((response) => {
            return response || caches.match('/')
              .then((fallbackResponse) => {
                return fallbackResponse || caches.match('/offline.html');
              });
          })
          .catch(() => {
            // If everything fails, show offline page
            return caches.match('/offline.html');
          })
      );
    } else {
      // Handle other static assets
      event.respondWith(
        caches.match(request)
          .then((response) => {
            return response || fetch(request)
              .then((response) => {
                // Cache the response
                const responseClone = response.clone();
                caches.open(STATIC_CACHE)
                  .then((cache) => {
                    cache.put(request, responseClone);
                  });
                return response;
              });
          })
      );
    }
  }
});

// Handle background sync
self.addEventListener('sync', (event) => {
  console.log('Background sync triggered:', event.tag);
  if (event.tag === 'sync-inspections') {
    event.waitUntil(syncInspections());
  }
  if (event.tag === 'sync-files') {
    event.waitUntil(syncFiles());
  }
});

let syncWorker = null;

function getSyncWorker() {
  if (!syncWorker) {
    syncWorker = new Worker('/sync-worker.js');
  }
  return syncWorker;
}

async function syncInspections() {
  console.log('Background sync: Syncing inspections...');
  try {
    const worker = getSyncWorker();
    worker.postMessage({ type: 'SYNC_INSPECTIONS' });
  } catch (error) {
    console.error('Background sync inspections failed:', error);
  }
}

async function syncFiles() {
  console.log('Background sync: Syncing files...');
  try {
    const worker = getSyncWorker();
    worker.postMessage({ type: 'SYNC_FILES' });
  } catch (error) {
    console.error('Background sync files failed:', error);
  }
}
import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Grid,
  Paper,
  Box,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import InfoIcon from '@mui/icons-material/Info';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  
  Legend,
  PieChart,
  Pie,
  Cell,
  ResponsiveContainer
} from 'recharts';
import { getInspectionsWithOffline } from '../services/api';

const DashboardPage = () => {
  const [inspections, setInspections] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedProject, setSelectedProject] = useState('');

  // Color schemes for charts
  const COLORS = {
    adequate: '#4CAF50',    // Green
    inadequate: '#F44336',  // Red
    certified: '#2196F3',   // Blue
    nonCertified: '#FF9800', // Orange
    readable: '#4CAF50',    // Green
    unreadable: '#F44336'   // Red
  };

  const PIE_COLORS = ['#4CAF50', '#F44336', '#2196F3', '#FF9800', '#9C27B0', '#607D8B', '#795548', '#E91E63'];

  useEffect(() => {
    fetchInspections();
  }, []);



  const fetchInspections = async () => {
    try {
      setLoading(true);
      const authToken = localStorage.getItem('authToken');
      const currentUsername = localStorage.getItem('currentUsername');

      if (!authToken || !currentUsername) {
        console.error('Dashboard: No authentication token found');
        setError('Utilisateur non connecté');
        setInspections([]);
        return;
      }

      // Fetch user-specific inspections using token authentication with offline support
      console.log('Dashboard: Fetching inspections for authenticated user:', currentUsername);
      const inspectionsData = await getInspectionsWithOffline();
      console.log('Dashboard: API response:', inspectionsData);
      console.log('Dashboard: Number of inspections:', inspectionsData?.length || 0);
      console.log('Dashboard: Number of inspections:', inspectionsData?.length || 0);

      if (inspectionsData && Array.isArray(inspectionsData)) {
        if (inspectionsData.length > 0) {
          console.log('Dashboard: Sample inspection data structure:', inspectionsData[0]);
          console.log('Dashboard: Available fields in first inspection:', Object.keys(inspectionsData[0]));

          // Log some key fields to verify data structure
          const sample = inspectionsData[0];
          console.log('Dashboard: Key ATEX fields in sample:', {
            atex_oui: sample.atex_oui,
            atex_non: sample.atex_non,
            marquage_atex_g: sample.marquage_atex_g,
            marquage_atex_d: sample.marquage_atex_d,
            marquage_us: sample.marquage_us,
            acces_illisible: sample.acces_illisible,
            acces_pas_plaque: sample.acces_pas_plaque,
            equipement: sample.equipement,
            unite: sample.unite,
            projet: sample.projet
          });
        }
        setInspections(inspectionsData);
        setError('');
        console.log('Dashboard: Successfully loaded', inspectionsData.length, 'inspections');
      } else {
        console.warn('Dashboard: Invalid data format received:', typeof inspectionsData);
        setInspections([]);
        setError('Format de données invalide');
      }
    } catch (err) {
      const errorMessage = err?.message || err?.error || 'Erreur lors du chargement des données';
      setError(errorMessage);
      console.error('Dashboard: Error fetching inspections:', err);
      console.error('Dashboard: Error details:', {
        message: err?.message,
        response: err?.response?.data,
        status: err?.response?.status
      });
      setInspections([]);
    } finally {
      setLoading(false);
    }
  };

  // Helper function to check if equipment is ATEX certified
  const isATEXCertified = (inspection) => {
    // Check if any ATEX marking is present and equipment has a plaque
    const hasATEXMarking = !!(inspection.marquage_atex_g || inspection.marquage_atex_d || inspection.marquage_us);
    const hasPlaque = !inspection.acces_pas_plaque;
    return hasATEXMarking && hasPlaque;
  };

  // Helper function to check ATEX adequacy
  const isATEXAdequate = (inspection) => {
    // Equipment is adequate if atex_oui is true and atex_non is false
    return inspection.atex_oui === true && inspection.atex_non !== true;
  };

  // Helper function to check marking readability
  const isMarkingReadable = (inspection) => {
    // Marking is readable if acces_illisible is false
    return !inspection.acces_illisible;
  };

  // Helper function to get equipment type (handle custom equipment)
  const getEquipmentType = (inspection) => {
    return inspection.equipement_custom || inspection.equipement || 'Type non spécifié';
  };

  // Helper function to get unit name (handle custom unit)
  const getUnitName = (inspection) => {
    return inspection.unite_custom || inspection.unite || inspection.localisation || 'Unité non spécifiée';
  };

  // Helper function to get unique projects for filter dropdown
  const getUniqueProjects = () => {
    const projects = inspections
      .map(inspection => inspection.projet)
      .filter(projet => projet && projet.trim())
      .map(projet => projet.trim().toUpperCase()); // Normalize to uppercase

    return [...new Set(projects)].sort();
  };

  // Helper function to filter inspections by selected project (case-insensitive)
  const getFilteredInspections = () => {
    if (!selectedProject) {
      return inspections;
    }

    return inspections.filter(inspection => {
      const inspectionProject = inspection.projet || '';
      return inspectionProject.toUpperCase().includes(selectedProject.toUpperCase());
    });
  };

  // 1. Data for equipment adequacy by unit (Bar Chart)
  const getAdequacyByUnit = () => {
    const unitData = {};
    const filteredInspections = getFilteredInspections();

    filteredInspections.forEach(inspection => {
      const unit = getUnitName(inspection);
      if (!unitData[unit]) {
        unitData[unit] = { unite: unit, nombre_adequats: 0, nombre_inadequats: 0 };
      }

      if (isATEXAdequate(inspection)) {
        unitData[unit].nombre_adequats++;
      } else {
        unitData[unit].nombre_inadequats++;
      }
    });

    const result = Object.values(unitData);
    console.log('Adequacy by unit data:', result);
    return result;
  };

  // 2. Data for global ATEX adequacy (Pie Chart)
  const getGlobalAdequacy = () => {
    let adequats = 0;
    let inadequats = 0;
    const filteredInspections = getFilteredInspections();

    filteredInspections.forEach(inspection => {
      if (isATEXAdequate(inspection)) {
        adequats++;
      } else {
        inadequats++;
      }
    });

    const total = adequats + inadequats;
    return [
      {
        name: 'Adéquats',
        value: adequats,
        percentage: total > 0 ? ((adequats / total) * 100).toFixed(1) : 0
      },
      {
        name: 'Inadéquats',
        value: inadequats,
        percentage: total > 0 ? ((inadequats / total) * 100).toFixed(1) : 0
      }
    ];
  };

  // 3. Data for inadequate equipment by type (Bar Chart)
  const getInadequateByType = () => {
    const typeData = {};
    const filteredInspections = getFilteredInspections();

    filteredInspections.forEach(inspection => {
      const type = getEquipmentType(inspection);
      if (!typeData[type]) {
        typeData[type] = { type_equipement: type, nombre_adequats: 0, nombre_inadequats: 0 };
      }

      if (isATEXAdequate(inspection)) {
        typeData[type].nombre_adequats++;
      } else {
        typeData[type].nombre_inadequats++;
      }
    });

    const result = Object.values(typeData);
    console.log('Inadequate by type data:', result);
    return result;
  };

  // 4. Data for ATEX certification (Pie Chart)
  const getATEXCertification = () => {
    let certified = 0;
    let nonCertified = 0;
    const filteredInspections = getFilteredInspections();

    filteredInspections.forEach(inspection => {
      if (isATEXCertified(inspection)) {
        certified++;
      } else {
        nonCertified++;
      }
    });

    const total = certified + nonCertified;
    return [
      {
        name: 'Certifiés ATEX',
        value: certified,
        percentage: total > 0 ? ((certified / total) * 100).toFixed(1) : 0
      },
      {
        name: 'Non certifiés ATEX',
        value: nonCertified,
        percentage: total > 0 ? ((nonCertified / total) * 100).toFixed(1) : 0
      }
    ];
  };

  // 5. Data for non-ATEX equipment by type (Pie Chart)
  const getNonATEXByType = () => {
    const typeData = {};
    const filteredInspections = getFilteredInspections();

    filteredInspections.forEach(inspection => {
      if (!isATEXCertified(inspection)) {
        const type = getEquipmentType(inspection);
        typeData[type] = (typeData[type] || 0) + 1;
      }
    });

    const total = Object.values(typeData).reduce((sum, count) => sum + count, 0);

    const result = Object.entries(typeData).map(([type, count]) => ({
      type,
      count,
      percentage: total > 0 ? ((count / total) * 100).toFixed(1) : 0
    }));

    console.log('Non-ATEX by type data:', result);
    return result;
  };

  // 6. Data for marking readability by unit (Bar Chart)
  const getReadabilityByUnit = () => {
    const unitData = {};
    const filteredInspections = getFilteredInspections();

    filteredInspections.forEach(inspection => {
      if (isATEXCertified(inspection)) {
        const unit = getUnitName(inspection);
        if (!unitData[unit]) {
          unitData[unit] = { unite: unit, lisibles: 0, illisibles: 0 };
        }

        if (isMarkingReadable(inspection)) {
          unitData[unit].lisibles++;
        } else {
          unitData[unit].illisibles++;
        }
      }
    });

    const result = Object.values(unitData);
    console.log('Readability by unit data:', result);
    return result;
  };


  const renderCustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, index, ...rest }) => {
  const RADIAN = Math.PI / 180;
  const radius = innerRadius + (outerRadius - innerRadius) * 1.2;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  const name = rest.name || rest.type;
  const percentage = rest.percentage || (percent * 100).toFixed(1);
  const displayLabel = `${name}: ${percentage}%`;

  // Determine label color from the rest.color or fallback color arrays
  const sliceColor =
    rest.fill ||
    rest.color ||
    PIE_COLORS?.[index % PIE_COLORS.length] || '#000'; // fallback to black

  return (
    <text
      x={x}
      y={y}
      fill={sliceColor}
      textAnchor={x > cx ? 'start' : 'end'}
      dominantBaseline="central"
      style={{
        fontSize: '12px',
        fontWeight: 500,
        maxWidth: '100px',
        overflow: 'hidden',
        whiteSpace: 'nowrap'
      }}
    >
      {displayLabel}
    </text>
  );
};


const renderCustomLabelSmall = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, index, ...rest }) => {
  const RADIAN = Math.PI / 180;
  const radius = innerRadius + (outerRadius - innerRadius) * 1.8; // wider radius
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  const name = rest.name || rest.type;
  const percentage = rest.percentage || (percent * 100).toFixed(1);
  const displayLabel = `${name}: ${percentage}%`;

  const sliceColor = rest.fill || PIE_COLORS?.[index % PIE_COLORS.length] || '#000';

  return (
    <text
      x={x}
      y={y}
      fill={sliceColor}
      textAnchor={x > cx ? 'start' : 'end'}
      dominantBaseline="central"
      style={{ fontSize: '12px', fontWeight: 500 }}
    >
      {displayLabel}
    </text>
  );
};

  // 7. Data for global CPF readability (Pie Chart)
  const getCPFReadability = () => {
    let readable = 0;
    let unreadable = 0;
    const filteredInspections = getFilteredInspections();

    filteredInspections.forEach(inspection => {
      // Filter for CPF site and ATEX certified equipment
      const isCPF = inspection.projet && inspection.projet.toLowerCase().includes('cpf');
      if (isCPF && isATEXCertified(inspection)) {
        if (isMarkingReadable(inspection)) {
          readable++;
        } else {
          unreadable++;
        }
      }
    });

    const total = readable + unreadable;
    return [
      {
        name: 'Lisibles',
        value: readable,
        percentage: total > 0 ? ((readable / total) * 100).toFixed(1) : 0
      },
      {
        name: 'Illisibles',
        value: unreadable,
        percentage: total > 0 ? ((unreadable / total) * 100).toFixed(1) : 0
      }
    ];
  };

  // 8. Data for unreadable ATEX equipment by type (Pie Chart)
  const getUnreadableATEXByType = () => {
    const typeData = {};
    const filteredInspections = getFilteredInspections();

    filteredInspections.forEach(inspection => {
      if (isATEXCertified(inspection) && !isMarkingReadable(inspection)) {
        const type = getEquipmentType(inspection);
        typeData[type] = (typeData[type] || 0) + 1;
      }
    });

    const total = Object.values(typeData).reduce((sum, count) => sum + count, 0);

    const result = Object.entries(typeData).map(([type, count]) => ({
      type,
      count,
      percentage: total > 0 ? ((count / total) * 100).toFixed(1) : 0
    }));

    console.log('Unreadable ATEX by type data:', result);
    return result;
  };

  // 9. Data for ATEX equipment by protection mode (Bar Chart)
  const getEquipmentByProtectionMode = () => {
    const modeData = {};
    const filteredInspections = getFilteredInspections();

    filteredInspections.forEach(inspection => {
      // Filter only ATEX certified equipment
      if (isATEXCertified(inspection)) {
        const mode = inspection.mode_protection;
        if (mode && mode.trim()) {
          // Format the mode with "Ex " prefix for display
          const formattedMode = `Ex ${mode}`;
          modeData[formattedMode] = (modeData[formattedMode] || 0) + 1;
        }
      }
    });

    const total = Object.values(modeData).reduce((sum, count) => sum + count, 0);

    const result = Object.entries(modeData).map(([mode_protection, count]) => ({
      mode_protection,
      count,
      pourcentage: total > 0 ? ((count / total) * 100).toFixed(1) : 0
    }));

    console.log('Equipment by protection mode data:', result);
    return result;
  };

  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ mt: 4, display: 'flex', justifyContent: 'center' }}>
        <CircularProgress />
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="xl" sx={{ mt: 4 }}>
        <Alert severity="error">{error}</Alert>
      </Container>
    );
  }

  return (
<Box
  sx={{
    backgroundColor: '#ffffff',
    minHeight: '100vh',
    ml: '280px',           // ← Keep sidebar offset
    pt: 2,                 // ← Small top padding (safe)
    pb: 4,
    px: 3,
    boxSizing: 'border-box'
  }}
>

      <Container maxWidth="xl" sx={{ position: 'relative', top: '-600px', zIndex: 1 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mb: 2 }}>
          <Typography variant="h3" component="h1" sx={{ color: '#1976d2', mr: 2 }}>
            Tableau de Bord - Statistiques ATEX
          </Typography>
          <IconButton
            onClick={fetchInspections}
            disabled={loading}
            sx={{ color: '#1976d2' }}
            title="Actualiser les données"
          >
            <RefreshIcon />
          </IconButton>
        </Box>

        {/* Project Filter */}
        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
          <FormControl sx={{ minWidth: 300 }}>
            <InputLabel id="project-filter-label">Filtrer par projet</InputLabel>
            <Select
              labelId="project-filter-label"
              value={selectedProject}
              label="Filtrer par projet"
              onChange={(e) => setSelectedProject(e.target.value)}
              sx={{ bgcolor: 'white' }}
            >
              <MenuItem value="">
                <em>Tous les projets</em>
              </MenuItem>
              {getUniqueProjects().map((project) => (
                <MenuItem key={project} value={project}>
                  {project}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>

        {getFilteredInspections().length === 0 && !loading && !error && (
          <Alert severity="info" sx={{ mb: 3 }}>
            <Typography variant="body2">
              📊 {selectedProject ? `Aucune inspection trouvée pour le projet "${selectedProject}".` : 'Aucune inspection trouvée. Créez des inspections pour voir les statistiques ATEX.'}
            </Typography>
          </Alert>
        )}

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            <Typography variant="body2">
              ⚠️ {error}
            </Typography>
          </Alert>
        )}



        

      {/* Summary Statistics */}
      <Grid container spacing={2} sx={{ mb: 4 }} justifyContent="center">
        <Grid item xs={12} sm={6} md={3}>
          <Tooltip
            title="Nombre total d'inspections réalisées."
            arrow
            placement="top"
          >
            <Paper sx={{ p: 2, textAlign: 'center', bgcolor: '#e3f2fd', cursor: 'help' }}>
              <Typography variant="h4" color="primary">{getFilteredInspections().length}</Typography>
              <Typography variant="body2">Total Inspections</Typography>
            </Paper>
          </Tooltip>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Tooltip
            title="Nombre d'équipements conformes au zonage ATEX."
            arrow
            placement="top"
          >
            <Paper sx={{ p: 2, textAlign: 'center', bgcolor: '#e8f5e8', cursor: 'help' }}>
              <Typography variant="h4" color="success.main">
                {getFilteredInspections().filter(i => isATEXAdequate(i)).length}
              </Typography>
              <Typography variant="body2">Équipements Adéquats</Typography>
            </Paper>
          </Tooltip>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Tooltip
            title="Nombre d'équipements disposant d'une certification ATEX."
            arrow
            placement="top"
          >
            <Paper sx={{ p: 2, textAlign: 'center', bgcolor: '#fff3e0', cursor: 'help' }}>
              <Typography variant="h4" color="warning.main">
                {getFilteredInspections().filter(i => isATEXCertified(i)).length}
              </Typography>
              <Typography variant="body2">Certifiés ATEX</Typography>
            </Paper>
          </Tooltip>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Tooltip
            title="Nombre d'équipements dont le marquage est illisible."
            arrow
            placement="top"
          >
            <Paper sx={{ p: 2, textAlign: 'center', bgcolor: '#fce4ec', cursor: 'help' }}>
              <Typography variant="h4" color="error.main">
                {getFilteredInspections().filter(i => isATEXCertified(i) && !isMarkingReadable(i)).length}
              </Typography>
              <Typography variant="body2">Marquage Illisible</Typography>
            </Paper>
          </Tooltip>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* 1. Bar Chart - Equipment adequacy by unit */}
        <Grid item xs={12} lg={6}>
          <Paper sx={{ p: 3, height: 450, display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mr: 1 }}>
                1. Répartition de l'adéquation par unité
              </Typography>
              <Tooltip
                title="Visualise pour chaque unité opérationnelle le nombre d'équipements conformes ou non conformes au zonage ATEX."
                arrow
                placement="top"
              >
                <InfoIcon sx={{ color: '#1976d2', fontSize: 20, cursor: 'help' }} />
              </Tooltip>
            </Box>
            <Box sx={{ flexGrow: 1, minHeight: 350 }}>
              {getAdequacyByUnit().length > 0 ? (
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={getAdequacyByUnit()} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="unite" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="nombre_adequats" fill={COLORS.adequate} name="Adéquats" />
                    <Bar dataKey="nombre_inadequats" fill={COLORS.inadequate} name="Inadéquats" />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                  <Typography color="text.secondary">Aucune donnée disponible</Typography>
                </Box>
              )}
            </Box>
            <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
              Total: {getAdequacyByUnit().length} unités
            </Typography>
          </Paper>
        </Grid>

        {/* 2. Pie Chart - Global ATEX adequacy */}
       <Grid item xs={12} lg={10} md={8}>
          <Paper sx={{ p: 3, height: 450, display: 'flex', flexDirection: 'column' , width: '100%'}}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mr: 1 }}>
                2. Adéquation globale ATEX
              </Typography>
              <Tooltip
                title="Donne une vue d'ensemble de la proportion d'équipements conformes et non conformes au zonage ATEX à l'échelle du site."
                arrow
                placement="top"
              >
                <InfoIcon sx={{ color: '#1976d2', fontSize: 20, cursor: 'help' }} />
              </Tooltip>
            </Box>
            <Box sx={{ flexGrow: 1, minHeight: 350 }}>
              {getFilteredInspections().length > 0 ? (
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={getGlobalAdequacy()}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={renderCustomLabel}
                      outerRadius={100}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {getGlobalAdequacy().map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={index === 0 ? COLORS.adequate : COLORS.inadequate} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value, name) => [`${value} équipements`, name]} />
                  </PieChart>
                </ResponsiveContainer>
              ) : (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                  <Typography color="text.secondary">Aucune donnée disponible</Typography>
                </Box>
              )}
            </Box>
            <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
              Total: {getFilteredInspections().length} équipements analysés
            </Typography>
          </Paper>
        </Grid>

        {/* 3. Bar Chart - Inadequate equipment by type */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: 400 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mr: 1 }}>
                3. Équipements inadéquats par type
              </Typography>
              <Tooltip
                title="Identifie les types d'équipements (moteurs, luminaires, armoires) les plus fréquemment non conformes au zonage ATEX."
                arrow
                placement="top"
              >
                <InfoIcon sx={{ color: '#1976d2', fontSize: 20, cursor: 'help' }} />
              </Tooltip>
            </Box>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={getInadequateByType()}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="type_equipement" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="nombre_adequats" fill={COLORS.adequate} name="Conformes" />
                <Bar dataKey="nombre_inadequats" fill={COLORS.inadequate} name="Non conformes" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Row containing only Chart 4 and 5 */}
<Grid item xs={12}>
  <Grid container spacing={3}>
    {/* Chart 4 - ATEX Certification (Full Width) */}
    <Grid item xs={12}>
      <Paper sx={{ p: 3, height: 400, display: 'flex', flexDirection: 'column' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Typography variant="h6" sx={{ fontWeight: 'bold', mr: 1 }}>
            4. Certification ATEX
          </Typography>
          <Tooltip
            title="Distingue les équipements inspectés disposant d'une certification ATEX de ceux qui n'en ont pas. Critères : Un équipement est non certifié ATEX si les marquages ATEX G, D et US sont vides/absents OU si 'pas de plaque' est coché. Sinon, il est certifié ATEX."
            arrow
            placement="top"
          >
            <InfoIcon sx={{ color: '#1976d2', fontSize: 20, cursor: 'help' }} />
          </Tooltip>
        </Box>
        <Box sx={{ flexGrow: 1, minHeight: 350 }}>
          {getFilteredInspections().length > 0 ? (
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={getATEXCertification()}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={renderCustomLabel}
                  outerRadius={100}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {getATEXCertification().map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={index === 0 ? COLORS.certified : COLORS.nonCertified} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          ) : (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
              <Typography color="text.secondary">Aucune donnée disponible</Typography>
            </Box>
          )}
        </Box>
        <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
          Total: {getFilteredInspections().length} équipements analysés
        </Typography>
      </Paper>
    </Grid>

    {/* Chart 5 - Non-ATEX Equipment by Type */}
    <Grid item xs={12} md={8}>
      <Paper sx={{ p: 3, height: 400 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Typography variant="h6" sx={{ fontWeight: 'bold', mr: 1 }}>
            5. Équipements non ATEX par type
          </Typography>
          <Tooltip
            title="Affiche la répartition des équipements non certifiés ATEX selon leur type (moteur, luminaire, armoire, etc.)."
            arrow
            placement="top"
          >
            <InfoIcon sx={{ color: '#1976d2', fontSize: 20, cursor: 'help' }} />
          </Tooltip>
        </Box>
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={getNonATEXByType()}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={renderCustomLabel}
              outerRadius={80}
              fill="#8884d8"
              dataKey="count"
            >
              {getNonATEXByType().map((entry, index) => (
                <Cell key={`cell-${index}`} fill={PIE_COLORS[index % PIE_COLORS.length]} />
              ))}
            </Pie>
            <Tooltip />
          </PieChart>
        </ResponsiveContainer>
      </Paper>
    </Grid>
  </Grid>
</Grid>


        {/* 6. Bar Chart - Marking readability by unit */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: 400 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mr: 1 }}>
                6. Lisibilité marquage par unité
              </Typography>
              <Tooltip
                title="Affiche par unité le nombre d'équipements ATEX inspectés dont le marquage est lisible ou illisible."
                arrow
                placement="top"
              >
                <InfoIcon sx={{ color: '#1976d2', fontSize: 20, cursor: 'help' }} />
              </Tooltip>
            </Box>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={getReadabilityByUnit()}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="unite" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="lisibles" fill={COLORS.readable} name="Lisibles" />
                <Bar dataKey="illisibles" fill={COLORS.unreadable} name="Illisibles" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* 7. Pie Chart - Global CPF readability */}
        <Grid item xs={12} md={10}>
          <Paper sx={{ p: 3, height: 400 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mr: 1 }}>
                7. Lisibilité globale CPF
              </Typography>
              <Tooltip
                title="Affiche la répartition globale dans le CPF (Central Processing Facility) des équipements ATEX selon la lisibilité de leur marquage."
                arrow
                placement="top"
              >
                <InfoIcon sx={{ color: '#1976d2', fontSize: 20, cursor: 'help' }} />
              </Tooltip>
            </Box>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={getCPFReadability()}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={renderCustomLabel}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {getCPFReadability().map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={index === 0 ? COLORS.readable : COLORS.unreadable} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* 8. Pie Chart - Unreadable ATEX equipment by type */}
        <Grid item xs={12} lg={12} md={10}>
          <Paper sx={{ p: 3, height: 400 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mr: 1 }}>
                8. Marquage illisible par type
              </Typography>
              <Tooltip
                title="Visualise la répartition des équipements ATEX dont le marquage est illisible, par type d'équipement."
                arrow
                placement="top"
              >
                <InfoIcon sx={{ color: '#1976d2', fontSize: 20, cursor: 'help' }} />
              </Tooltip>
            </Box>
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={getUnreadableATEXByType()}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={renderCustomLabel}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="count"
                >
                  {getUnreadableATEXByType().map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={PIE_COLORS[index % PIE_COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* 9. Bar Chart - Equipment by protection mode */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, height: 450, display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mr: 1 }}>
                9. Équipements inspectés par mode de protection
              </Typography>
              <Tooltip
                title="Affiche la répartition des équipements ATEX inspectés selon les modes de protection ATEX (Ex d, Ex e, Ex i, Ex n, etc.). Seuls les équipements certifiés ATEX sont inclus dans cette analyse."
                arrow
                placement="top"
              >
                <InfoIcon sx={{ color: '#1976d2', fontSize: 20, cursor: 'help' }} />
              </Tooltip>
            </Box>
            <Box sx={{ flexGrow: 1, minHeight: 350 }}>
              {getEquipmentByProtectionMode().length > 0 ? (
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={getEquipmentByProtectionMode()} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="mode_protection" />
                    <YAxis />
                    <Tooltip formatter={(value, name) => [`${value} équipements (${getEquipmentByProtectionMode().find(item => item.count === value)?.pourcentage || 0}%)`, 'Nombre d\'équipements']} />
                    <Legend />
                    <Bar dataKey="count" fill="#2196F3" name="Nombre d'équipements">
                      {getEquipmentByProtectionMode().map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={PIE_COLORS[index % PIE_COLORS.length]} />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                  <Typography color="text.secondary">Aucune donnée disponible</Typography>
                </Box>
              )}
            </Box>
            <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
              Total: {getEquipmentByProtectionMode().reduce((sum, item) => sum + item.count, 0)} équipements ATEX
            </Typography>
          </Paper>
        </Grid>
      </Grid>
    </Container>
    </Box>
  );
};

export default DashboardPage;
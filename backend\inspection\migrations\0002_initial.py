# Generated by Django 5.1 on 2025-06-11 15:02

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('inspection', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Inspection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('fiche_num', models.CharField(max_length=100)),
                ('date', models.DateField()),
                ('projet', models.CharField(max_length=200)),
                ('equipement', models.CharField(max_length=200)),
                ('tag', models.Char<PERSON>ield(max_length=100)),
                ('constructeur', models.CharField(max_length=200)),
                ('model', models.CharField(max_length=200)),
                ('puissance', models.Char<PERSON>ield(max_length=100)),
                ('courant', models.Char<PERSON>ield(max_length=100)),
                ('tension', models.<PERSON>r<PERSON><PERSON>(max_length=100)),
                ('unite', models.CharField(max_length=100)),
                ('localisation', models.CharField(max_length=200)),
                ('zone_atex', models.CharField(max_length=100)),
                ('groupe_gaz', models.CharField(max_length=100)),
                ('classe_t', models.CharField(max_length=100)),
                ('marquage_atex_g', models.CharField(max_length=100)),
                ('marquage_atex_d', models.CharField(max_length=100)),
                ('marquage_us_cl', models.CharField(max_length=100)),
                ('marquage_us_div', models.CharField(max_length=100)),
                ('marquage_us_gr', models.CharField(max_length=100)),
                ('marquage_us_cl2', models.CharField(max_length=100)),
                ('marquage_us_div2', models.CharField(max_length=100)),
                ('marquage_us_gr2', models.CharField(max_length=100)),
                ('type_marquage', models.CharField(max_length=100)),
                ('mode_protection', models.CharField(max_length=100)),
                ('organisme_notifie', models.CharField(max_length=100)),
                ('ip', models.CharField(max_length=100)),
                ('nema', models.CharField(max_length=100)),
                ('certificat', models.CharField(max_length=100)),
                ('tamb_min', models.CharField(max_length=100)),
                ('tamb_max', models.CharField(max_length=100)),
                ('atex_oui', models.BooleanField(default=False)),
                ('atex_non', models.BooleanField(default=False)),
                ('acces_inaccessible', models.BooleanField(default=False)),
                ('acces_calorifuge', models.BooleanField(default=False)),
                ('acces_peinte', models.BooleanField(default=False)),
                ('acces_inaccessible_plaque', models.BooleanField(default=False)),
                ('acces_illisible', models.BooleanField(default=False)),
                ('acces_pas_plaque', models.BooleanField(default=False)),
                ('points', models.JSONField()),
                ('observations', models.TextField()),
                ('action', models.TextField()),
                ('pdf_file', models.FileField(blank=True, null=True, upload_to='inspection_pdfs/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]

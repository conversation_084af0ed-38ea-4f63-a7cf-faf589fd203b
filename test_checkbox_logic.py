#!/usr/bin/env python3
"""
Test script to verify the checkbox logic is working correctly
"""

def simple_checkbox(checked):
    """Return checkmark (✓) for checked, blank for unchecked"""
    return "✓" if checked else ""

def test_checkbox_logic():
    """Test the checkbox logic with various scenarios"""
    
    print("=== CHECKBOX LOGIC TEST ===")
    print()
    
    # Test basic checkbox function
    print("Basic checkbox tests:")
    print(f"True -> '{simple_checkbox(True)}'")
    print(f"False -> '{simple_checkbox(False)}'")
    print(f"None -> '{simple_checkbox(None)}'")
    print(f"0 -> '{simple_checkbox(0)}'")
    print(f"1 -> '{simple_checkbox(1)}'")
    print(f"'' -> '{simple_checkbox('')}'")
    print(f"'text' -> '{simple_checkbox('text')}'")
    print()
    
    # Test with inspection-like data
    print("Inspection data simulation:")
    
    # Simulate inspection object
    class MockInspection:
        def __init__(self):
            self.atex_oui = True
            self.atex_non = False
            self.acces_inaccessible = False
            self.acces_calorifuge = True
            self.acces_peinte = False
            self.points = {
                'etatGeneral': {'correct': True, 'defaut': False, 'remarques': 'Good condition'},
                'identification': {'correct': False, 'defaut': True, 'remarques': 'Missing label'},
                'partieVerre': {'correct': False, 'defaut': False, 'remarques': 'Not applicable'},
            }
    
    inspection = MockInspection()
    
    print("ATEX Adequate:")
    print(f"  Oui: {simple_checkbox(inspection.atex_oui)}")
    print(f"  Non: {simple_checkbox(inspection.atex_non)}")
    print()
    
    print("Access:")
    print(f"  Appareil Inaccessible: {simple_checkbox(inspection.acces_inaccessible)}")
    print(f"  Appareil sous Calorifuge: {simple_checkbox(inspection.acces_calorifuge)}")
    print(f"  Plaque Peinte: {simple_checkbox(inspection.acces_peinte)}")
    print()
    
    print("Inspection Points:")
    for point_key, point_data in inspection.points.items():
        is_correct = point_data.get('correct', False)
        is_defaut = point_data.get('defaut', False)
        remarques = point_data.get('remarques', '')

        correct_check = simple_checkbox(is_correct)
        defaut_check = simple_checkbox(is_defaut)
        
        print(f"  {point_key}:")
        print(f"    Correct: '{correct_check}'")
        print(f"    Défaut: '{defaut_check}'")
        print(f"    Remarques: {remarques}")
        print()
    
    print("=== TEST COMPLETED ===")
    print("All checkboxes should show checkmarks (✓) for checked items and blank for unchecked!")

if __name__ == "__main__":
    test_checkbox_logic()

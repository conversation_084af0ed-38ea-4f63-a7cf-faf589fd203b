// IndexedDB utility for offline data storage
const DB_NAME = 'EInspectionDB';
const DB_VERSION = 1;

const STORES = {
  INSPECTIONS: 'inspections',
  USER_FILES: 'userFiles',
  PENDING_UPLOADS: 'pendingUploads',
  SYNC_QUEUE: 'syncQueue'
};

// Open IndexedDB connection
export const openDB = () => {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(DB_NAME, DB_VERSION);

    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve(request.result);

    request.onupgradeneeded = (event) => {
      const db = event.target.result;

      // Create object stores if they don't exist
      if (!db.objectStoreNames.contains(STORES.INSPECTIONS)) {
        const inspectionsStore = db.createObjectStore(STORES.INSPECTIONS, { keyPath: 'id' });
        inspectionsStore.createIndex('syncStatus', 'syncStatus', { unique: false });
        inspectionsStore.createIndex('lastModified', 'lastModified', { unique: false });
      }

      if (!db.objectStoreNames.contains(STORES.USER_FILES)) {
        const userFilesStore = db.createObjectStore(STORES.USER_FILES, { keyPath: 'id' });
        userFilesStore.createIndex('folderId', 'folderId', { unique: false });
        userFilesStore.createIndex('syncStatus', 'syncStatus', { unique: false });
      }

      if (!db.objectStoreNames.contains(STORES.PENDING_UPLOADS)) {
        const pendingUploadsStore = db.createObjectStore(STORES.PENDING_UPLOADS, { keyPath: 'id', autoIncrement: true });
        pendingUploadsStore.createIndex('type', 'type', { unique: false });
        pendingUploadsStore.createIndex('timestamp', 'timestamp', { unique: false });
      }

      if (!db.objectStoreNames.contains(STORES.SYNC_QUEUE)) {
        const syncQueueStore = db.createObjectStore(STORES.SYNC_QUEUE, { keyPath: 'id', autoIncrement: true });
        syncQueueStore.createIndex('operation', 'operation', { unique: false });
        syncQueueStore.createIndex('timestamp', 'timestamp', { unique: false });
      }
    };
  });
};

// Generic CRUD operations
export const addToStore = async (storeName, data) => {
  const db = await openDB();
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([storeName], 'readwrite');
    const store = transaction.objectStore(storeName);
    const request = store.add(data);

    request.onsuccess = () => resolve(request.result);
    request.onerror = () => reject(request.error);
  });
};

export const getFromStore = async (storeName, key) => {
  const db = await openDB();
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([storeName], 'readonly');
    const store = transaction.objectStore(storeName);
    const request = store.get(key);

    request.onsuccess = () => resolve(request.result);
    request.onerror = () => reject(request.error);
  });
};

export const getAllFromStore = async (storeName) => {
  const db = await openDB();
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([storeName], 'readonly');
    const store = transaction.objectStore(storeName);
    const request = store.getAll();

    request.onsuccess = () => resolve(request.result);
    request.onerror = () => reject(request.error);
  });
};

export const updateInStore = async (storeName, data) => {
  const db = await openDB();
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([storeName], 'readwrite');
    const store = transaction.objectStore(storeName);
    const request = store.put(data);

    request.onsuccess = () => resolve(request.result);
    request.onerror = () => reject(request.error);
  });
};

export const deleteFromStore = async (storeName, key) => {
  const db = await openDB();
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([storeName], 'readwrite');
    const store = transaction.objectStore(storeName);
    const request = store.delete(key);

    request.onsuccess = () => resolve(request.result);
    request.onerror = () => reject(request.error);
  });
};

// Specific functions for inspections
export const saveInspectionLocally = async (inspection) => {
  const inspectionData = {
    ...inspection,
    syncStatus: 'synced',
    lastModified: new Date().toISOString()
  };
  return await updateInStore(STORES.INSPECTIONS, inspectionData);
};

export const getLocalInspections = async () => {
  return await getAllFromStore(STORES.INSPECTIONS);
};

// Specific functions for user files
export const saveUserFileLocally = async (fileData) => {
  const file = {
    ...fileData,
    syncStatus: 'synced',
    lastModified: new Date().toISOString()
  };
  return await updateInStore(STORES.USER_FILES, file);
};

// PDF caching functions
export const savePDFLocally = async (inspectionId, pdfBlob, metadata = {}) => {
  const pdfData = {
    id: `pdf_${inspectionId}`,
    inspectionId,
    blob: pdfBlob,
    metadata: {
      ...metadata,
      cachedAt: new Date().toISOString(),
      size: pdfBlob.size
    }
  };
  return await updateInStore(STORES.USER_FILES, pdfData);
};

export const getPDFLocally = async (inspectionId) => {
  const pdfData = await getFromStore(STORES.USER_FILES, `pdf_${inspectionId}`);
  return pdfData;
};

export const hasPDFCached = async (inspectionId) => {
  const pdfData = await getPDFLocally(inspectionId);
  return !!pdfData;
};

// Bulk PDF operations
export const cacheAllPDFs = async (inspections, apiInstance) => {
  const results = {
    cached: 0,
    failed: 0,
    skipped: 0
  };

  for (const inspection of inspections) {
    try {
      // Check if already cached
      const existing = await hasPDFCached(inspection.id);
      if (existing) {
        results.skipped++;
        continue;
      }

      // Fetch and cache PDF
      const response = await apiInstance.get(`/api/inspections/inspections/${inspection.id}/generate_pdf/`, {
        responseType: 'blob'
      });

      const pdfBlob = new Blob([response.data], { type: 'application/pdf' });
      await savePDFLocally(inspection.id, pdfBlob, {
        ficheNum: inspection.fiche_num,
        date: inspection.date,
        cachedFromServer: true,
        bulkCached: true
      });

      results.cached++;
    } catch (error) {
      console.error(`Failed to cache PDF for inspection ${inspection.id}:`, error);
      results.failed++;
    }
  }

  return results;
};

export const getAllCachedPDFs = async () => {
  const db = await openDB();
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([STORES.USER_FILES], 'readonly');
    const store = transaction.objectStore(STORES.USER_FILES);
    const request = store.getAll();

    request.onsuccess = () => {
      const pdfs = request.result.filter(item => item.id && item.id.startsWith('pdf_'));
      resolve(pdfs);
    };
    request.onerror = () => reject(request.error);
  });
};

export const clearOldPDFs = async (daysOld = 30) => {
  const db = await openDB();
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysOld);

  return new Promise((resolve, reject) => {
    const transaction = db.transaction([STORES.USER_FILES], 'readwrite');
    const store = transaction.objectStore(STORES.USER_FILES);
    const request = store.getAll();

    request.onsuccess = () => {
      const pdfs = request.result.filter(item =>
        item.id && item.id.startsWith('pdf_') &&
        item.metadata?.cachedAt &&
        new Date(item.metadata.cachedAt) < cutoffDate
      );

      let deleted = 0;
      pdfs.forEach(pdf => {
        store.delete(pdf.id);
        deleted++;
      });

      resolve(deleted);
    };
    request.onerror = () => reject(request.error);
  });
};

export const getLocalUserFiles = async (folderId = null) => {
  const allFiles = await getAllFromStore(STORES.USER_FILES);
  if (folderId) {
    return allFiles.filter(file => file.folderId === folderId);
  }
  return allFiles;
};

// Queue operations for sync
export const addToSyncQueue = async (operation, data) => {
  const queueItem = {
    operation,
    data,
    timestamp: new Date().toISOString(),
    retryCount: 0
  };
  return await addToStore(STORES.SYNC_QUEUE, queueItem);
};

export const getSyncQueue = async () => {
  return await getAllFromStore(STORES.SYNC_QUEUE);
};

export const removeFromSyncQueue = async (id) => {
  return await deleteFromStore(STORES.SYNC_QUEUE, id);
};

// Pending uploads
export const addPendingUpload = async (type, data) => {
  const pendingItem = {
    type,
    data,
    timestamp: new Date().toISOString()
  };
  return await addToStore(STORES.PENDING_UPLOADS, pendingItem);
};

export const getPendingUploads = async () => {
  return await getAllFromStore(STORES.PENDING_UPLOADS);
};

export const removePendingUpload = async (id) => {
  return await deleteFromStore(STORES.PENDING_UPLOADS, id);
};
{"file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/einspection/__init__.py": {"language": "Python", "code": 0, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/einspection/wsgi.py": {"language": "Python", "code": 4, "comment": 8, "blank": 5}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/einspection/settings.py": {"language": "Python", "code": 119, "comment": 33, "blank": 36}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/test_db.py": {"language": "Python", "code": 22, "comment": 2, "blank": 3}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/requirements.txt": {"language": "pip requirements", "code": 10, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/einspection/asgi.py": {"language": "Python", "code": 4, "comment": 8, "blank": 5}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/inspection/models.py": {"language": "Python", "code": 58, "comment": 1, "blank": 5}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/einspection/urls.py": {"language": "Python", "code": 10, "comment": 16, "blank": 3}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/manage.py": {"language": "Python", "code": 15, "comment": 3, "blank": 5}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/inspection/urls.py": {"language": "Python", "code": 9, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/inspection/views.py": {"language": "Python", "code": 1360, "comment": 161, "blank": 178}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/inspection/serializers.py": {"language": "Python", "code": 52, "comment": 2, "blank": 9}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/inspection/migrations/0008_inspection_fiche_generation_method.py": {"language": "Python", "code": 12, "comment": 1, "blank": 6}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/inspection/migrations/0007_remove_inspection_fiche_generation_method_and_more.py": {"language": "Python", "code": 131, "comment": 1, "blank": 6}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/inspection/migrations/0006_inspection_fiche_generation_method.py": {"language": "Python", "code": 12, "comment": 1, "blank": 6}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/inspection/migrations/0005_inspection_photo_equipement_and_more.py": {"language": "Python", "code": 22, "comment": 1, "blank": 6}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/inspection/migrations/__init__.py": {"language": "Python", "code": 0, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/inspection/migrations/0002_initial.py": {"language": "Python", "code": 62, "comment": 1, "blank": 7}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/inspection/migrations/0004_remove_inspection_marquage_us_cl_and_more.py": {"language": "Python", "code": 71, "comment": 1, "blank": 6}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/inspection/migrations/0003_auto_20250611_1602.py": {"language": "Python", "code": 7, "comment": 1, "blank": 6}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/inspection/migrations/0001_initial.py": {"language": "Python", "code": 6, "comment": 1, "blank": 6}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/build.sh": {"language": "<PERSON> Script", "code": 18, "comment": 6, "blank": 7}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/config/__init__.py": {"language": "Python", "code": 0, "comment": 1, "blank": 0}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/config/wsgi.py": {"language": "Python", "code": 4, "comment": 8, "blank": 3}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/config/settings.py": {"language": "Python", "code": 144, "comment": 13, "blank": 18}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/config/urls.py": {"language": "Python", "code": 11, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/config/asgi.py": {"language": "Python", "code": 4, "comment": 8, "blank": 4}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/authentication/admin.py": {"language": "Python", "code": 1, "comment": 1, "blank": 2}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/authentication/apps.py": {"language": "Python", "code": 4, "comment": 0, "blank": 3}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/authentication/views.py": {"language": "Python", "code": 125, "comment": 14, "blank": 37}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/authentication/urls.py": {"language": "Python", "code": 12, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/authentication/__init__.py": {"language": "Python", "code": 0, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/authentication/tests.py": {"language": "Python", "code": 1, "comment": 1, "blank": 2}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/authentication/serializers.py": {"language": "Python", "code": 27, "comment": 0, "blank": 5}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/authentication/models.py": {"language": "Python", "code": 14, "comment": 1, "blank": 4}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/authentication/migrations/0001_initial.py": {"language": "Python", "code": 21, "comment": 1, "blank": 7}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/authentication/migrations/__init__.py": {"language": "Python", "code": 0, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/package.json": {"language": "JSON", "code": 5, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/Documents/ilhem/APSEC/e-inspection/backend/package-lock.json": {"language": "JSON", "code": 268, "comment": 0, "blank": 1}}
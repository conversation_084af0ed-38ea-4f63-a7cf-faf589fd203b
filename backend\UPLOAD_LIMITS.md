# Photo Upload Configuration

## Current Upload Limits

### Django Settings (backend/einspection/settings.py)
- `FILE_UPLOAD_MAX_MEMORY_SIZE`: 50MB
- `DATA_UPLOAD_MAX_MEMORY_SIZE`: 50MB  
- `DATA_UPLOAD_MAX_NUMBER_FIELDS`: 1000
- Total request size limit: 40MB (enforced in views.py)

### Per-File Limits (backend/inspection/views.py)
- Single photo files (photo_marquage, photo_equipement): 15MB each
- Anomaly photos: 15MB each
- Allowed file types: image/jpeg, image/jpg, image/png, image/gif, image/webp

### Frontend Compression (frontend/src/pages/InspectionFormPage.js)
- Compression threshold: 1MB (files larger than 1MB get compressed)
- photo_marquage/photo_equipement: Max 1000px width, quality 0.7, target max 3MB
- photos_anomalies: Max 800px width, quality 0.6, target max 2MB
- Adaptive quality reduction to meet size targets

## Error Handling

### 413 Request Entity Too Large
- Occurs when total upload size exceeds server limits
- Frontend shows user-friendly message with suggestions
- Backend validates total size before processing

### 500 Internal Server Error  
- Enhanced error handling for file save failures
- Automatic cleanup of partially uploaded files
- Detailed error messages for debugging

## Troubleshooting

### If uploads still fail:
1. Check server nginx/apache upload limits
2. Verify disk space on server
3. Check file permissions on media directories
4. Monitor server logs for detailed error messages

### For users experiencing issues:
1. Reduce image quality/resolution before upload
2. Upload photos one at a time instead of all together
3. Use supported image formats (JPG, PNG recommended)
4. Ensure stable internet connection for large uploads

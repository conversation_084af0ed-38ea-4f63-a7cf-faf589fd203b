# Generated by Django 5.1 on 2025-06-21 21:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inspection', '0004_remove_inspection_marquage_us_cl_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='inspection',
            name='photo_equipement',
            field=models.ImageField(blank=True, null=True, upload_to='inspection_photos/'),
        ),
        migrations.AddField(
            model_name='inspection',
            name='photo_marquage',
            field=models.ImageField(blank=True, null=True, upload_to='inspection_photos/'),
        ),
        migrations.AddField(
            model_name='inspection',
            name='photos_anomalies',
            field=models.JSONField(blank=True, default=list),
        ),
    ]

# Generated by Django 5.1 on 2025-07-27 15:11

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inspection', '0008_inspection_fiche_generation_method'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UserFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('folder_id', models.CharField(max_length=100)),
                ('name', models.CharField(max_length=255)),
                ('size', models.PositiveIntegerField()),
                ('upload_date', models.DateTimeField(auto_now_add=True)),
                ('type', models.CharField(blank=True, max_length=100, null=True)),
                ('file', models.FileField(upload_to='user_files/')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_files', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]

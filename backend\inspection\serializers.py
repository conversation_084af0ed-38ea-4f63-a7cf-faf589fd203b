from rest_framework import serializers
from .models import Inspection, UserFile, Company, CompanyLogo
import json

class JSONField(serializers.Field):
    """Custom field to handle JSON data that might come as strings in FormData"""

    def __init__(self, default_value=None, **kwargs):
        self.default_value = default_value
        super().__init__(**kwargs)

    def to_internal_value(self, data):
        if data is None or data == '' or data == 'null':
            return self.default_value if self.default_value is not None else {}

        if isinstance(data, str):
            try:
                parsed = json.loads(data)
                return parsed
            except (json.JSONDecodeError, TypeError) as e:
                print(f"DEBUG: JSONField parsing error: {e}, data: {repr(data)[:200]}")
                # Return default value instead of raising error
                return self.default_value if self.default_value is not None else {}
        elif isinstance(data, (dict, list)):
            return data
        else:
            print(f"DEBUG: JSONField unexpected type: {type(data)}, data: {repr(data)[:200]}")
            # Return default value instead of raising error
            return self.default_value if self.default_value is not None else {}

    def to_representation(self, value):
        return value

class InspectionSerializer(serializers.ModelSerializer):
    equipement_custom = serializers.CharField(required=False, allow_blank=True)
    unite_custom = serializers.CharField(required=False, allow_blank=True)
    marquage_us = serializers.CharField(required=False, allow_blank=True)
    points = JSONField(default_value={})
    photos_anomalies = JSONField(default_value=[])

    photo_marquage = serializers.ImageField(required=False, allow_null=True)
    photo_equipement = serializers.ImageField(required=False, allow_null=True)

    class Meta:
        model = Inspection
        fields = [
            'id', 'fiche_num', 'fiche_generation_method', 'date', 'projet', 'equipement', 'equipement_custom', 'tag',
            'constructeur', 'model', 'numero_serie', 'date_installation', 'age', 'puissance', 'courant', 'tension',
            'unite', 'unite_custom', 'localisation', 'zone_atex', 'groupe_gaz', 'classe_t',
            'marquage_atex_g', 'marquage_atex_d', 'marquage_us', 'type_marquage',
            'mode_protection', 'organisme_notifie', 'ip', 'nema',
            'certificat', 'tamb_min', 'tamb_max', 'atex_oui', 'atex_non',
            'acces_inaccessible', 'acces_calorifuge', 'acces_peinte',
            'acces_inaccessible_plaque', 'acces_illisible',
            'acces_pas_plaque', 'niveau_1', 'niveau_2', 'niveau_3',
            'points', 'observations', 'action',
            'date_precedente_inspection', 'inspecteur', 'qualifications', 'numero_certificat', 'observations_complementaires',
            'photo_marquage', 'photo_equipement', 'photos_anomalies', 'inspected_company_name',
            'created_at', 'created_by', 'pdf_file'
        ]
        read_only_fields = ('created_at', 'created_by', 'pdf_file')

class UserFileSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserFile
        fields = ['id', 'user', 'folder_id', 'name', 'size', 'upload_date', 'type', 'file']
        read_only_fields = ['id', 'user', 'upload_date']  # 'file' is NOT read-only here!

    def to_representation(self, instance):
        """Customize output to use secure /content/ endpoint"""
        data = super().to_representation(instance)
        if instance.file and data['file']:
            request = self.context.get('request')
            # Replace media URL with secure API endpoint
            content_url = f"/api/inspections/userfiles/{instance.id}/content/"
            data['file'] = request.build_absolute_uri(content_url) if request else f"http://***************{content_url}"
        return data


class CompanySerializer(serializers.ModelSerializer):
    class Meta:
        model = Company
        fields = ['id', 'name', 'created_at', 'updated_at']
        read_only_fields = ('created_at', 'updated_at')


class CompanyLogoSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyLogo
        fields = ['id', 'logo_type', 'company_name', 'logo', 'uploaded_at', 'updated_at']
        read_only_fields = ('uploaded_at', 'updated_at')

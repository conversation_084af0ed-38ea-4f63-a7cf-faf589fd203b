import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Grid,
  Card,
  CardContent,
  Divider,
  InputAdornment,
} from '@mui/material';
import {
  Download as DownloadIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  FilterList as FilterIcon,
  Clear as ClearIcon,
  Search as SearchIcon,
 
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import api, { getInspections, getInspectionsWithOffline } from '../services/api';
import { savePDFLocally, getPDFLocally, hasPDFCached, cacheAllPDFs, getAllCachedPDFs } from '../utils/indexedDB';
import { isOnline } from '../utils/offlineUtils';
import { CloudDownload as CloudDownloadIcon, Cached as CachedIcon } from '@mui/icons-material';

// PDF Cache Status Indicator Component
const PDFCacheIndicator = ({ inspectionId, isCached }) => {
  return (
    <Chip
      size="small"
      label="PDF"
      color={isCached ? "success" : "default"}
      variant="outlined"
      sx={{ ml: 1, fontSize: '0.7rem', height: '20px' }}
      title={isCached ? "PDF cached for offline access" : "PDF not cached"}
    />
  );
};

const Documents = () => {
  const [inspections, setInspections] = useState([]);
  const [filteredInspections, setFilteredInspections] = useState([]);
  const [selectedPdf, setSelectedPdf] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [bulkCaching, setBulkCaching] = useState(false);
  const [cacheProgress, setCacheProgress] = useState({ current: 0, total: 0 });
  const [cachedPdfIds, setCachedPdfIds] = useState(new Set());

  const navigate = useNavigate();
  const location = useLocation();

  // Filter states
  const [filters, setFilters] = useState({
    date: '',
    project: '',
    niveau: '',
    equipement: ''
  });

  // Search state
  const [searchQuery, setSearchQuery] = useState('');

  // Available filter options
  const [filterOptions, setFilterOptions] = useState({
    projects: [],
    equipements: [],
    niveaux: ['N1', 'N2', 'N3']
  });

  useEffect(() => {
    fetchInspections();
    loadCachedPdfIds();
  }, []);

  useEffect(() => {
    // If navigated back with refresh flag, refetch inspections
    if (location.state?.refresh) {
      fetchInspections();
      // Clear the refresh flag so it doesn't refetch again unnecessarily
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);

  useEffect(() => {
    // Debounce search to avoid excessive filtering
    const timeoutId = setTimeout(() => {
      applyFilters();
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [inspections, filters, searchQuery]);

  const fetchInspections = async () => {
    try {
      // Check if user is authenticated
      const authToken = localStorage.getItem('authToken');
      const currentUsername = localStorage.getItem('currentUsername');

      if (!authToken || !currentUsername) {
        console.error('Documents: No authentication token found');
        setInspections([]);
        return;
      }

      // Fetch user-specific inspections using token authentication with offline support
      console.log('Documents: Fetching inspections for authenticated user:', currentUsername);
      const inspectionsData = await getInspectionsWithOffline();
      console.log('Documents: API response:', inspectionsData);
      console.log('Documents: Number of inspections:', inspectionsData?.length || 0);

      setInspections(inspectionsData || []);

      // Extract unique values for filter options
      const projects = [...new Set(inspectionsData.map(item => item.projet).filter(Boolean))];
      const equipements = [...new Set(inspectionsData.map(item => {
        return item.equipement === 'custom' && item.equipement_custom
          ? item.equipement_custom
          : item.equipement;
      }).filter(Boolean))];

      setFilterOptions({
        projects: projects.sort(),
        equipements: equipements.sort(),
        niveaux: ['N1', 'N2', 'N3']
      });
    } catch (error) {
      console.error('Error fetching inspections:', error);
    }
  };

  const applyFilters = () => {
    let filtered = [...inspections];

    // Search filter - apply first to search across all data
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(item => {
        // Search across multiple fields
        const searchableFields = [
          item.fiche_num || '',
          item.projet || '',
          item.equipement === 'custom' && item.equipement_custom
            ? item.equipement_custom
            : item.equipement || '',
          item.tag || '',
          item.localisation || '',
          item.date || '',
          // Include niveau in search
          formatNiveaux(item)
        ];

        return searchableFields.some(field =>
          field.toString().toLowerCase().includes(query)
        );
      });
    }

    // Date filter
    if (filters.date) {
      filtered = filtered.filter(item => item.date === filters.date);
    }

    // Project filter
    if (filters.project) {
      filtered = filtered.filter(item => item.projet === filters.project);
    }

    // Equipment filter
    if (filters.equipement) {
      filtered = filtered.filter(item => {
        const equipement = item.equipement === 'custom' && item.equipement_custom
          ? item.equipement_custom
          : item.equipement;
        return equipement === filters.equipement;
      });
    }

    // Niveau filter
    if (filters.niveau) {
      filtered = filtered.filter(item => {
        switch (filters.niveau) {
          case 'N1': return item.niveau_1;
          case 'N2': return item.niveau_2;
          case 'N3': return item.niveau_3;
          default: return true;
        }
      });
    }

    // Sort by creation date (earliest to latest) for sequential numbering
    // Use created_at if available, otherwise fall back to date field
    filtered.sort((a, b) => {
      const dateA = new Date(a.created_at || a.date);
      const dateB = new Date(b.created_at || b.date);
      return dateA - dateB;
    });

    setFilteredInspections(filtered);
  };

  const handleFilterChange = (filterName, value) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value
    }));
  };

  const clearFilters = () => {
    setFilters({
      date: '',
      project: '',
      niveau: '',
      equipement: ''
    });
    setSearchQuery('');
  };

  const clearSearch = () => {
    setSearchQuery('');
  };

  const getActiveFiltersCount = () => {
    const filterCount = Object.values(filters).filter(value => value !== '').length;
    const searchCount = searchQuery.trim() ? 1 : 0;
    return filterCount + searchCount;
  };

  const handleSearchChange = (value) => {
    setSearchQuery(value);
  };



  const handleViewPdf = async (inspectionId) => {
    try {
      console.log('Attempting to view PDF for inspection ID:', inspectionId);

      let pdfBlob;
      let url;

      // Check if PDF is cached locally
      const cachedPdf = await getPDFLocally(inspectionId);

      if (cachedPdf) {
        console.log('Using cached PDF for inspection ID:', inspectionId);
        pdfBlob = cachedPdf.blob;
        url = window.URL.createObjectURL(pdfBlob);
      } else if (isOnline()) {
        console.log('Fetching PDF from server for inspection ID:', inspectionId);
        const response = await api.get(`/api/inspections/inspections/${inspectionId}/generate_pdf/`, {
          responseType: 'blob'
        });
        console.log('PDF response received:', response);
        pdfBlob = new Blob([response.data], { type: 'application/pdf' });

        // Cache the PDF for offline access
        await savePDFLocally(inspectionId, pdfBlob, {
          ficheNum: 'cached_pdf',
          cachedFromServer: true
        });

        // Update cached PDF IDs
        setCachedPdfIds(prev => new Set([...prev, inspectionId.toString()]));

        url = window.URL.createObjectURL(pdfBlob);
      } else {
        alert('PDF is not cached locally and you are offline. Please connect to the internet to view PDFs.');
        return;
      }

      setSelectedPdf(url);
      setOpenDialog(true);
    } catch (error) {
      console.error('Error fetching PDF:', error);
      alert(`Error generating PDF: ${error.message || 'Unknown error'}`);
    }
  };

  const handleDownloadPdf = async (inspectionId, ficheNum, date) => {
    try {
      console.log('Attempting to download PDF for inspection ID:', inspectionId);

      let pdfBlob;

      // Check if PDF is cached locally
      const cachedPdf = await getPDFLocally(inspectionId);

      if (cachedPdf) {
        console.log('Using cached PDF for download, inspection ID:', inspectionId);
        pdfBlob = cachedPdf.blob;
      } else if (isOnline()) {
        console.log('Fetching PDF from server for download, inspection ID:', inspectionId);
        const response = await api.get(`/api/inspections/inspections/${inspectionId}/generate_pdf/`, {
          responseType: 'blob'
        });
        console.log('PDF download response received:', response);
        pdfBlob = new Blob([response.data], { type: 'application/pdf' });

        // Cache the PDF for future offline access
        await savePDFLocally(inspectionId, pdfBlob, {
          ficheNum,
          date,
          cachedFromServer: true
        });

        // Update cached PDF IDs
        setCachedPdfIds(prev => new Set([...prev, inspectionId.toString()]));
      } else {
        alert('PDF is not cached locally and you are offline. Please connect to the internet to download PDFs.');
        return;
      }

      const url = window.URL.createObjectURL(pdfBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `inspection_${ficheNum}_${date}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      console.log('PDF downloaded successfully');
    } catch (error) {
      console.error('Error downloading PDF:', error);
      alert(`Error downloading PDF: ${error.message || 'Unknown error'}`);
    }
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    if (selectedPdf) {
      window.URL.revokeObjectURL(selectedPdf);
      setSelectedPdf(null);
    }
  };

  const handleEditInspection = (inspection) => {
    // Debug: Log the inspection data being passed
    console.log('Edit inspection data:', inspection);
    console.log('Inspection keys:', Object.keys(inspection));
    console.log('Is pending inspection:', inspection.isPending);

    // Handle pending inspections differently
    if (inspection.isPending) {
      // For pending inspections, we need to get the original data from localStorage
      try {
        const pendingInspections = JSON.parse(localStorage.getItem('pendingInspections') || '[]');
        const originalPendingData = pendingInspections.find(p => p.fiche_num === inspection.fiche_num);

        if (originalPendingData) {
          console.log('Found original pending data:', originalPendingData);

          // Use the original pending data but ensure all required fields
          const completeInspectionData = {
            ...originalPendingData,
            // Ensure all required fields are present with defaults if missing
            fiche_num: originalPendingData.fiche_num || '',
            fiche_generation_method: originalPendingData.fiche_generation_method || 'manual',
            date: originalPendingData.date || '',
            projet: originalPendingData.projet || '',
            equipement: originalPendingData.equipement || '',
            equipement_custom: originalPendingData.equipement_custom || '',
            tag: originalPendingData.tag || '',
            constructeur: originalPendingData.constructeur || '',
            model: originalPendingData.model || '',
            numero_serie: originalPendingData.numero_serie || '',
            date_installation: originalPendingData.date_installation || '',
            age: originalPendingData.age || '',
            puissance: originalPendingData.puissance || '',
            courant: originalPendingData.courant || '',
            tension: originalPendingData.tension || '',
            unite: originalPendingData.unite || '',
            unite_custom: originalPendingData.unite_custom || '',
            localisation: originalPendingData.localisation || '',
            zone_atex: originalPendingData.zone_atex || '',
            groupe_gaz: originalPendingData.groupe_gaz || '',
            classe_t: originalPendingData.classe_t || '',
            marquage_atex_g: originalPendingData.marquage_atex_g || '',
            marquage_atex_d: originalPendingData.marquage_atex_d || '',
            marquage_us: originalPendingData.marquage_us || '',
            type_marquage: originalPendingData.type_marquage || '',
            mode_protection: originalPendingData.mode_protection || '',
            organisme_notifie: originalPendingData.organisme_notifie || '',
            ip: originalPendingData.ip || '',
            nema: originalPendingData.nema || '',
            certificat: originalPendingData.certificat || '',
            tamb_min: originalPendingData.tamb_min || '',
            tamb_max: originalPendingData.tamb_max || '',
            atex_oui: originalPendingData.atex_oui || false,
            atex_non: originalPendingData.atex_non || false,
            acces_inaccessible: originalPendingData.acces_inaccessible || false,
            acces_calorifuge: originalPendingData.acces_calorifuge || false,
            acces_peinte: originalPendingData.acces_peinte || false,
            acces_inaccessible_plaque: originalPendingData.acces_inaccessible_plaque || false,
            acces_illisible: originalPendingData.acces_illisible || false,
            acces_pas_plaque: originalPendingData.acces_pas_plaque || false,
            niveau_1: originalPendingData.niveau_1 || false,
            niveau_2: originalPendingData.niveau_2 || false,
            niveau_3: originalPendingData.niveau_3 || false,
            points: originalPendingData.points || {},
            action: originalPendingData.action || '',
            date_precedente_inspection: originalPendingData.date_precedente_inspection || '',
            inspecteur: originalPendingData.inspecteur || '',
            qualifications: originalPendingData.qualifications || '',
            numero_certificat: originalPendingData.numero_certificat || '',
            observations_complementaires: originalPendingData.observations_complementaires || '',
            inspected_company_name: originalPendingData.inspected_company_name || '',
            // Mark as pending for special handling
            isPending: true
          };

          console.log('Complete pending inspection data for editing:', completeInspectionData);

          // Navigate to inspection form with the inspection data for editing
          navigate('/InspectionFormPage', {
            state: {
              editData: completeInspectionData,
              isEdit: true,
              isPending: true
            }
          });
          return;
        }
      } catch (error) {
        console.error('Error retrieving pending inspection data:', error);
      }
    }

    // Handle regular (synced) inspections
    const completeInspectionData = {
      ...inspection,
      // Ensure all required fields are present with defaults if missing
      fiche_num: inspection.fiche_num || '',
      fiche_generation_method: inspection.fiche_generation_method || 'manual',
      date: inspection.date || '',
      projet: inspection.projet || '',
      equipement: inspection.equipement || '',
      equipement_custom: inspection.equipement_custom || '',
      tag: inspection.tag || '',
      constructeur: inspection.constructeur || '',
      model: inspection.model || '',
      numero_serie: inspection.numero_serie || '',
      date_installation: inspection.date_installation || '',
      age: inspection.age || '',
      puissance: inspection.puissance || '',
      courant: inspection.courant || '',
      tension: inspection.tension || '',
      unite: inspection.unite || '',
      unite_custom: inspection.unite_custom || '',
      localisation: inspection.localisation || '',
      zone_atex: inspection.zone_atex || '',
      groupe_gaz: inspection.groupe_gaz || '',
      classe_t: inspection.classe_t || '',
      marquage_atex_g: inspection.marquage_atex_g || '',
      marquage_atex_d: inspection.marquage_atex_d || '',
      marquage_us: inspection.marquage_us || '',
      type_marquage: inspection.type_marquage || '',
      mode_protection: inspection.mode_protection || '',
      organisme_notifie: inspection.organisme_notifie || '',
      ip: inspection.ip || '',
      nema: inspection.nema || '',
      certificat: inspection.certificat || '',
      tamb_min: inspection.tamb_min || '',
      tamb_max: inspection.tamb_max || '',
      atex_oui: inspection.atex_oui || false,
      atex_non: inspection.atex_non || false,
      acces_inaccessible: inspection.acces_inaccessible || false,
      acces_calorifuge: inspection.acces_calorifuge || false,
      acces_peinte: inspection.acces_peinte || false,
      acces_inaccessible_plaque: inspection.acces_inaccessible_plaque || false,
      acces_illisible: inspection.acces_illisible || false,
      acces_pas_plaque: inspection.acces_pas_plaque || false,
      niveau_1: inspection.niveau_1 || false,
      niveau_2: inspection.niveau_2 || false,
      niveau_3: inspection.niveau_3 || false,
      points: inspection.points || {},
      action: inspection.action || '',
      date_precedente_inspection: inspection.date_precedente_inspection || '',
      inspecteur: inspection.inspecteur || '',
      qualifications: inspection.qualifications || '',
      numero_certificat: inspection.numero_certificat || '',
      observations_complementaires: inspection.observations_complementaires || '',
      inspected_company_name: inspection.inspected_company_name || ''
    };

    console.log('Complete inspection data for editing:', completeInspectionData);

    // Navigate to inspection form with the inspection data for editing
    navigate('/InspectionFormPage', {
      state: {
        editData: completeInspectionData,
        isEdit: true
      }
    });
  };

  const formatNiveaux = (inspection) => {
    const niveaux = [];
    if (inspection.niveau_1) niveaux.push('N1');
    if (inspection.niveau_2) niveaux.push('N2');
    if (inspection.niveau_3) niveaux.push('N3');
    return niveaux.length > 0 ? niveaux.join(', ') : '-';
  };

  const loadCachedPdfIds = async () => {
    try {
      const cachedPdfs = await getAllCachedPDFs();
      const pdfIds = new Set(cachedPdfs.map(pdf => pdf.id.replace('pdf_', '')));
      setCachedPdfIds(pdfIds);
    } catch (error) {
      console.error('Error loading cached PDF IDs:', error);
    }
  };

  const handleBulkCachePDFs = async () => {
    if (!isOnline()) {
      alert('La mise en cache nécessite une connexion internet');
      return;
    }

    if (filteredInspections.length === 0) {
      alert('Aucune inspection à mettre en cache');
      return;
    }

    setBulkCaching(true);
    setCacheProgress({ current: 0, total: filteredInspections.length });

    try {
      let cached = 0;
      let skipped = 0;
      let failed = 0;

      for (let i = 0; i < filteredInspections.length; i++) {
        const inspection = filteredInspections[i];
        setCacheProgress({ current: i + 1, total: filteredInspections.length });

        try {
          // Check if already cached
          const existing = cachedPdfIds.has(inspection.id.toString());
          if (existing) {
            skipped++;
            continue;
          }

          // Fetch and cache PDF
          const response = await api.get(`/api/inspections/inspections/${inspection.id}/generate_pdf/`, {
            responseType: 'blob'
          });

          const pdfBlob = new Blob([response.data], { type: 'application/pdf' });
          await savePDFLocally(inspection.id, pdfBlob, {
            ficheNum: inspection.fiche_num,
            date: inspection.date,
            cachedFromServer: true,
            bulkCached: true
          });

          // Update cached PDF IDs immediately
          setCachedPdfIds(prev => new Set([...prev, inspection.id.toString()]));

          cached++;
        } catch (error) {
          console.error(`Failed to cache PDF for inspection ${inspection.id}:`, error);
          failed++;
        }
      }

      alert(`Mise en cache terminée:\n${cached} PDFs mis en cache\n${skipped} PDFs déjà en cache\n${failed} PDFs échoués`);
    } catch (error) {
      console.error('Bulk caching error:', error);
      alert('Erreur lors de la mise en cache en masse');
    } finally {
      setBulkCaching(false);
      setCacheProgress({ current: 0, total: 0 });
    }
  };





  return (
    <Box sx={{
      p: 0,
      marginLeft: '280px',
      marginTop: '-600px',
      marginRight: '0px'
    }}>
     

      {/* Filters Section */}
      <Card sx={{ marginLeft: '20px', marginRight: '20px', marginBottom: '20px' }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', marginBottom: 2 }}>
            <FilterIcon sx={{ marginRight: 1 }} />
            <Typography variant="h6">
              Filtres
              {getActiveFiltersCount() > 0 && (
                <Chip
                  label={`${getActiveFiltersCount()} active`}
                  size="small"
                  color="primary"
                  sx={{ marginLeft: 1 }}
                />
              )}
            </Typography>
            <Box sx={{ flexGrow: 1 }} />
            <Button
              startIcon={<ClearIcon />}
              onClick={clearFilters}
              disabled={getActiveFiltersCount() === 0}
              size="small"
              sx={{ marginRight: 1 }}
            >
              Clear Filters
            </Button>

            {/* Bulk PDF Cache Button */}
            <Button
              startIcon={bulkCaching ? <CachedIcon /> : <CloudDownloadIcon />}
              onClick={handleBulkCachePDFs}
              disabled={!isOnline() || bulkCaching || filteredInspections.length === 0}
              size="small"
              variant="outlined"
              color="secondary"
              sx={{ marginRight: 1 }}
            >
              {bulkCaching ? `Caching... ${cacheProgress.current}/${cacheProgress.total}` : 'Cache All PDFs'}
            </Button>
          </Box>

          <Divider sx={{ marginBottom: 2 }} />

          {/* Search Section */}
          <Box sx={{ marginBottom: 3 }}>
            <TextField
              label="Rechercher des inspections..."
              value={searchQuery}
              onChange={(e) => handleSearchChange(e.target.value)}
              fullWidth
              size="medium"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
                endAdornment: searchQuery && (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={clearSearch}
                      edge="end"
                      size="small"
                      title="Clear search"
                    >
                      <ClearIcon />
                    </IconButton>
                  </InputAdornment>
                )
              }}
              placeholder="Rechercher par numéro de fiche, projet, équipement, tag, emplacement, date ou niveau..."
            />
          </Box>

          <Grid container spacing={3}>
            {/* Date Filter */}
            <Grid item xs={12} sm={6} md={2}>
              <TextField
                label="Date"
                type="date"
                value={filters.date}
                onChange={(e) => handleFilterChange('date', e.target.value)}
                slotProps={{
                  inputLabel: { shrink: true }
                }}
                fullWidth
                size="medium"
              />
            </Grid>

            {/* Project Filter */}
            <Grid item xs={12} sm={6} md={4}>
              <FormControl fullWidth size="medium">
                <InputLabel>Projet</InputLabel>
                <Select
                  value={filters.project}
                  onChange={(e) => handleFilterChange('project', e.target.value)}
                  label="Project"
                  sx={{ minWidth: 200 }}
                >
                  <MenuItem value="">Tous les Projets</MenuItem>
                  {filterOptions.projects.map((project) => (
                    <MenuItem key={project} value={project}>
                      {project}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Equipment Filter */}
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth size="medium">
                <InputLabel>Équipement</InputLabel>
                <Select
                  value={filters.equipement}
                  onChange={(e) => handleFilterChange('equipement', e.target.value)}
                  label="Equipment"
                  sx={{ minWidth: 180 }}
                >
                  <MenuItem value="">Tous les Équipement</MenuItem>
                  {filterOptions.equipements.map((equipement) => (
                    <MenuItem key={equipement} value={equipement}>
                      {equipement}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Niveau Filter */}
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth size="medium">
                <InputLabel>Niveau</InputLabel>
                <Select
                  value={filters.niveau}
                  onChange={(e) => handleFilterChange('niveau', e.target.value)}
                  label="Niveau"
                  sx={{ minWidth: 150 }}
                >
                  <MenuItem value="">Tou les Niveaux</MenuItem>
                  {filterOptions.niveaux.map((niveau) => (
                    <MenuItem key={niveau} value={niveau}>
                      {niveau}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
      {/* Results Summary */}
      <Box sx={{ marginLeft: '20px', marginBottom: '10px' }}>
        <Typography variant="body2" color="text.secondary">
          Showing {filteredInspections.length} of {inspections.length} inspections
          {searchQuery.trim() && ' (searched)'}
          {Object.values(filters).some(value => value !== '') && ' (filtered)'}
          {searchQuery.trim() && Object.values(filters).some(value => value !== '') && ' (searched & filtered)'}
        </Typography>
      </Box>

      <TableContainer
        component={Paper}
        sx={{
          marginLeft: '20px',
          marginTop: '10px'
        }}
      >
        <Table>
          <TableHead>
            <TableRow>
              <TableCell></TableCell>
              <TableCell>Numéro de Fiche </TableCell>
              <TableCell>Date</TableCell>
              <TableCell>Projet</TableCell>
              <TableCell>Entreprise</TableCell>
              <TableCell>Équipement</TableCell>
              <TableCell>Niveau</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredInspections.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} align="center" sx={{ py: 4 }}>
                  <Typography variant="body1" color="text.secondary">
                    {inspections.length === 0
                      ? 'No inspections found.'
                      : searchQuery.trim() && Object.values(filters).some(value => value !== '')
                      ? 'No inspections match the current search and filters.'
                      : searchQuery.trim()
                      ? 'No inspections match the current search.'
                      : 'No inspections match the current filters.'}
                  </Typography>
                  {getActiveFiltersCount() > 0 && inspections.length > 0 && (
                    <Button
                      onClick={clearFilters}
                      size="small"
                      sx={{ mt: 1 }}
                    >
                      Clear All Filters & Search
                    </Button>
                  )}
                </TableCell>
              </TableRow>
            ) : (
              filteredInspections.map((inspection, index) => (
                <TableRow key={inspection.id}>
                  <TableCell sx={{ fontWeight: 'bold', color: 'text.secondary' }}>
                    {String(index + 1).padStart(2, '0')}
                  </TableCell>
                  <TableCell>{inspection.fiche_num}</TableCell>
                  <TableCell>{inspection.date}</TableCell>
                  <TableCell>{inspection.projet}</TableCell>
                  <TableCell>{inspection.inspected_company_name || '-'}</TableCell>
                  <TableCell>
                    {inspection.equipement === 'custom' && inspection.equipement_custom
                      ? inspection.equipement_custom
                      : inspection.equipement}
                  </TableCell>
                  <TableCell>
                    <Box sx={{
                      display: 'flex',
                      gap: 0.5,
                      flexWrap: 'wrap'
                    }}>
                      {formatNiveaux(inspection)}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <IconButton
                      onClick={() => handleViewPdf(inspection.id)}
                      color="primary"
                      title="View PDF"
                      size="small"
                    >
                      <VisibilityIcon />
                    </IconButton>
                    <IconButton
                      onClick={() => handleDownloadPdf(inspection.id, inspection.fiche_num, inspection.date)}
                      color="primary"
                      title="Download PDF"
                      size="small"
                    >
                      <DownloadIcon />
                    </IconButton>
                    <IconButton
                      onClick={() => handleEditInspection(inspection)}
                      color="primary"
                      title="Edit (Create New)"
                      size="small"
                    >
                      <EditIcon />
                    </IconButton>
                    {/* PDF Cache Status Indicator */}
                    <PDFCacheIndicator
                      inspectionId={inspection.id}
                      isCached={cachedPdfIds.has(inspection.id.toString())}
                    />
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Inspection PDF</DialogTitle>
        <DialogContent>
          {selectedPdf && (
            <iframe
              src={selectedPdf}
              style={{ width: '100%', height: '80vh' }}
              title="PDF Viewer"
            />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Close</Button>
        </DialogActions>
      </Dialog>


    </Box>
  );
};

export default Documents;
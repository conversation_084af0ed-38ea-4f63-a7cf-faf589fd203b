# Generated by Django 5.1 on 2025-08-06 19:31

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inspection', '0012_inspection_age_inspection_date_installation_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='companylogo',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='companylogo',
            name='company_name',
            field=models.CharField(blank=True, help_text='Name of the company this logo belongs to', max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='inspection',
            name='inspected_company_name',
            field=models.Char<PERSON>ield(blank=True, help_text='Name of the company being inspected', max_length=200, null=True),
        ),
        migrations.AlterUniqueTogether(
            name='companylogo',
            unique_together={('user', 'logo_type', 'company_name')},
        ),
    ]

// Topbar.js

import React, { useState, useEffect } from 'react';
import { Box, IconButton, Typography, <PERSON>, Badge } from "@mui/material";
import NotificationsIcon from '@mui/icons-material/Notifications';
import SettingsIcon from '@mui/icons-material/Settings';
import WifiIcon from '@mui/icons-material/Wifi';
import WifiOffIcon from '@mui/icons-material/WifiOff';
import SyncIcon from '@mui/icons-material/Sync';
import { useNavigate } from 'react-router-dom';
import { syncService } from '../../services/syncService';

const Topbar = ({ isOnline }) => {
  const navigate = useNavigate();
  const [pendingCount, setPendingCount] = useState(0);

  useEffect(() => {
    // Update pending count periodically
    const updatePendingCount = () => {
      const count = syncService.getPendingInspectionsCount();
      setPendingCount(count);
    };

    updatePendingCount();
    const interval = setInterval(updatePendingCount, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, []);

  const handleManualSync = async () => {
    if (!isOnline) {
      alert('Synchronisation impossible hors ligne');
      return;
    }

    try {
      await syncService.manualSync();
      // Refresh pending count after sync
      setPendingCount(syncService.getPendingInspectionsCount());
      alert('Synchronisation terminée');
    } catch (error) {
      console.error('Sync error:', error);
      alert('Erreur lors de la synchronisation');
    }
  };

  const handleSettingsClick = () => {
    navigate('/settingsMain'); // Navigate to the settings page
  };

  return (
    <Box
      display="flex"
      justifyContent="space-between"
      alignItems="center"
      p={2}
      sx={{
        backgroundColor: '#f9f9fc',
        position: 'fixed',
        top: 0,
        left: '280px', // Adjust this value based on your sidebar width
        right: 0,
        zIndex: 1000,
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
      }}
    >
      {/* Online Status Indicator */}
      <Box display="flex" alignItems="center">
        <Chip
          icon={isOnline ? <WifiIcon /> : <WifiOffIcon />}
          label={isOnline ? "Online" : "Offline"}
          color={isOnline ? "success" : "warning"}
          variant="outlined"
          size="small"
        />
      </Box>

      {/* ICONS */}
      <Box display="flex" alignItems="center">
        {/* Pending Inspections Indicator */}
        {pendingCount > 0 && (
          <Chip
            label={`${pendingCount} en attente`}
            color="warning"
            size="small"
            sx={{ mr: 1 }}
            title={`${pendingCount} inspection(s) en attente de synchronisation`}
          />
        )}

        {/* Manual Sync Button */}
        <IconButton
          onClick={handleManualSync}
          disabled={!isOnline}
          title={isOnline ? "Synchroniser maintenant" : "Synchronisation impossible hors ligne"}
          sx={{ mr: 1 }}
        >
          <Badge badgeContent={pendingCount} color="warning">
            <SyncIcon sx={{ color: isOnline ? '#004381' : '#ccc', fontSize: '30px' }} />
          </Badge>
        </IconButton>

        <IconButton>
          <NotificationsIcon sx={{ color: '#004381', fontSize: '30px' }} /> {/* Set color and size */}
        </IconButton>
        <IconButton onClick={handleSettingsClick}>
          <SettingsIcon sx={{ color: '#004381', fontSize: '30px' }} /> {/* Set color and size */}
        </IconButton>
      </Box>
    </Box>
  );
};

export default Topbar;
from django.core.management.base import BaseCommand
from django.db import transaction
from inspection.models import Inspection
import os
from django.conf import settings


class Command(BaseCommand):
    help = 'Delete all existing inspections and their associated files to start fresh with new F,G,C structure'

    def add_arguments(self, parser):
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='Confirm that you want to delete all inspections',
        )

    def handle(self, *args, **options):
        if not options['confirm']:
            self.stdout.write(
                self.style.WARNING(
                    'This command will delete ALL existing inspections and their files.\n'
                    'This action cannot be undone!\n'
                    'Run with --confirm flag if you are sure: python manage.py clear_old_inspections --confirm'
                )
            )
            return

        try:
            with transaction.atomic():
                # Get all inspections
                inspections = Inspection.objects.all()
                count = inspections.count()
                
                if count == 0:
                    self.stdout.write(self.style.SUCCESS('No inspections found to delete.'))
                    return

                self.stdout.write(f'Found {count} inspections to delete...')

                # Delete associated files
                deleted_files = 0
                for inspection in inspections:
                    # Delete photo files
                    if inspection.photo_marquage:
                        try:
                            if os.path.exists(inspection.photo_marquage.path):
                                os.remove(inspection.photo_marquage.path)
                                deleted_files += 1
                        except Exception as e:
                            self.stdout.write(f'Warning: Could not delete photo_marquage: {e}')

                    if inspection.photo_equipement:
                        try:
                            if os.path.exists(inspection.photo_equipement.path):
                                os.remove(inspection.photo_equipement.path)
                                deleted_files += 1
                        except Exception as e:
                            self.stdout.write(f'Warning: Could not delete photo_equipement: {e}')

                    # Delete anomaly photos
                    if inspection.photos_anomalies:
                        for photo_path in inspection.photos_anomalies:
                            try:
                                full_path = os.path.join(settings.MEDIA_ROOT, photo_path)
                                if os.path.exists(full_path):
                                    os.remove(full_path)
                                    deleted_files += 1
                            except Exception as e:
                                self.stdout.write(f'Warning: Could not delete anomaly photo {photo_path}: {e}')

                    # Delete PDF file if it exists
                    if inspection.pdf_file:
                        try:
                            if os.path.exists(inspection.pdf_file.path):
                                os.remove(inspection.pdf_file.path)
                                deleted_files += 1
                        except Exception as e:
                            self.stdout.write(f'Warning: Could not delete PDF file: {e}')

                # Delete all inspection records
                inspections.delete()

                self.stdout.write(
                    self.style.SUCCESS(
                        f'Successfully deleted {count} inspections and {deleted_files} associated files.\n'
                        'Database is now clean and ready for new inspections with F,G,C structure.'
                    )
                )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error occurred while deleting inspections: {str(e)}')
            )
            raise

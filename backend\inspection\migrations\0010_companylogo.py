# Generated by Django 5.1 on 2025-07-29 13:58

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inspection', '0009_userfile'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CompanyLogo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('logo_type', models.CharField(choices=[('performing_company', 'Performing Company'), ('inspected_company', 'Inspected Company')], max_length=20)),
                ('logo', models.ImageField(upload_to='company_logos/')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='company_logos', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('user', 'logo_type')},
            },
        ),
    ]

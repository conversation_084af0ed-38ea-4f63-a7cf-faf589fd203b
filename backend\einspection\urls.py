"""
URL configuration for einspection project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include, re_path
from django.http import JsonResponse, HttpResponse, Http404, FileResponse
from django.conf import settings
from django.conf.urls.static import static
from rest_framework.authtoken.models import Token
import os
import mimetypes

def health_check(request):
    return JsonResponse({
        "status": "healthy",
        "message": "E-Inspection API is running",
        "settings_module": "einspection.settings",
        "project": "einspection"
    })

def secure_media_serve(request, path):
    """
    Secure media file serving with authentication - EINSPECTION VERSION
    """
    print(f"DEBUG EINSPECTION: ===== SECURE MEDIA SERVE CALLED =====")
    print(f"DEBUG EINSPECTION: Path: {path}")
    print(f"DEBUG EINSPECTION: Request method: {request.method}")
    print(f"DEBUG EINSPECTION: Request URL: {request.build_absolute_uri()}")

    # Handle CORS preflight requests
    if request.method == 'OPTIONS':
        print("DEBUG EINSPECTION: Handling CORS preflight request")
        response = HttpResponse()
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, HEAD, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Authorization, Content-Type'
        response['Access-Control-Max-Age'] = '86400'
        return response

    # Check for token authentication
    auth_header = request.META.get('HTTP_AUTHORIZATION', '')
    print(f"DEBUG EINSPECTION: Auth header: {auth_header}")

    if auth_header.startswith('Token '):
        token_key = auth_header.split(' ')[1]
        try:
            token = Token.objects.get(key=token_key)
            user = token.user
            print(f"DEBUG EINSPECTION: Authenticated user via token: {user.username}")
        except Token.DoesNotExist:
            print("DEBUG EINSPECTION: Token not found")
            return HttpResponse('Unauthorized - Invalid token', status=401)
    elif request.user.is_authenticated:
        user = request.user
        print(f"DEBUG EINSPECTION: Authenticated user via session: {user.username}")
    else:
        print("DEBUG EINSPECTION: No authentication found")
        return HttpResponse('Unauthorized - No authentication', status=401)

    # Construct the full file path
    full_path = os.path.join(settings.MEDIA_ROOT, path)
    print(f"DEBUG EINSPECTION: Full file path: {full_path}")
    print(f"DEBUG EINSPECTION: MEDIA_ROOT: {settings.MEDIA_ROOT}")

    # Check if file exists
    if not os.path.exists(full_path):
        print(f"DEBUG EINSPECTION: File does not exist: {full_path}")
        raise Http404("File not found")

    print(f"DEBUG EINSPECTION: File exists, size: {os.path.getsize(full_path)} bytes")

    # Serve the file
    try:
        content_type = mimetypes.guess_type(full_path)[0] or 'application/octet-stream'
        print(f"DEBUG EINSPECTION: Serving file with content type: {content_type}")

        # Open file and create response
        file_handle = open(full_path, 'rb')
        response = FileResponse(
            file_handle,
            content_type=content_type,
            as_attachment=False
        )

        # Add proper headers for file serving
        response['Content-Length'] = os.path.getsize(full_path)
        response['Accept-Ranges'] = 'bytes'

        # Add CORS headers for cross-origin requests
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, HEAD, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Authorization, Content-Type'

        print(f"DEBUG EINSPECTION: File response created successfully, size: {os.path.getsize(full_path)} bytes")
        return response

    except IOError as e:
        print(f"DEBUG EINSPECTION: IOError serving file: {str(e)}")
        raise Http404("File not found")

urlpatterns = [
    path('', health_check, name='health_check'),
    path('admin/', admin.site.urls),
    path('api/auth/', include('authentication.urls')),
    path('api/inspections/', include('inspection.urls')),
    # Secure media serving
    re_path(r'^media/(?P<path>.*)$', secure_media_serve, name='secure_media'),
]

# Serve static files
urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

from django.urls import path
from .views import RegisterView, LoginView, LogoutView, UserProfileView, CSRFTokenView, ForgotPasswordView, ResetPasswordView

urlpatterns = [
    path('csrf/', CSRFTokenView.as_view(), name='csrf'),
    path('register/', RegisterView.as_view(), name='register'),
    path('login/', LoginView.as_view(), name='login'),
    path('logout/', LogoutView.as_view(), name='logout'),
    path('profile/', UserProfileView.as_view(), name='profile'),
    path('user/', UserProfileView.as_view(), name='user'),  # Alternative endpoint
    path('forgot-password/', ForgotPasswordView.as_view(), name='forgot_password'),
    path('reset-password/', ResetPasswordView.as_view(), name='reset_password'),
]
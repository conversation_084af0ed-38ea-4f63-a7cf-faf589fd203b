"filename", "language", "Python", "pip requirements", "Shell Script", "JSON", "comment", "blank", "total"
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\authentication\__init__.py", "Python", 0, 0, 0, 0, 0, 1, 1
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\authentication\admin.py", "Python", 1, 0, 0, 0, 1, 2, 4
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\authentication\apps.py", "Python", 4, 0, 0, 0, 0, 3, 7
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\authentication\migrations\0001_initial.py", "Python", 21, 0, 0, 0, 1, 7, 29
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\authentication\migrations\__init__.py", "Python", 0, 0, 0, 0, 0, 1, 1
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\authentication\models.py", "Python", 14, 0, 0, 0, 1, 4, 19
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\authentication\serializers.py", "Python", 27, 0, 0, 0, 0, 5, 32
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\authentication\tests.py", "Python", 1, 0, 0, 0, 1, 2, 4
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\authentication\urls.py", "Python", 12, 0, 0, 0, 0, 1, 13
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\authentication\views.py", "Python", 125, 0, 0, 0, 14, 37, 176
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\build.sh", "Shell Script", 0, 0, 18, 0, 6, 7, 31
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\config\__init__.py", "Python", 0, 0, 0, 0, 1, 0, 1
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\config\asgi.py", "Python", 4, 0, 0, 0, 8, 4, 16
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\config\settings.py", "Python", 144, 0, 0, 0, 13, 18, 175
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\config\urls.py", "Python", 11, 0, 0, 0, 0, 1, 12
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\config\wsgi.py", "Python", 4, 0, 0, 0, 8, 3, 15
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\einspection\__init__.py", "Python", 0, 0, 0, 0, 0, 1, 1
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\einspection\asgi.py", "Python", 4, 0, 0, 0, 8, 5, 17
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\einspection\settings.py", "Python", 119, 0, 0, 0, 33, 36, 188
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\einspection\urls.py", "Python", 10, 0, 0, 0, 16, 3, 29
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\einspection\wsgi.py", "Python", 4, 0, 0, 0, 8, 5, 17
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\migrations\0001_initial.py", "Python", 6, 0, 0, 0, 1, 6, 13
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\migrations\0002_initial.py", "Python", 62, 0, 0, 0, 1, 7, 70
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\migrations\0003_auto_20250611_1602.py", "Python", 7, 0, 0, 0, 1, 6, 14
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\migrations\0004_remove_inspection_marquage_us_cl_and_more.py", "Python", 71, 0, 0, 0, 1, 6, 78
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\migrations\0005_inspection_photo_equipement_and_more.py", "Python", 22, 0, 0, 0, 1, 6, 29
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\migrations\0006_inspection_fiche_generation_method.py", "Python", 12, 0, 0, 0, 1, 6, 19
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\migrations\0007_remove_inspection_fiche_generation_method_and_more.py", "Python", 131, 0, 0, 0, 1, 6, 138
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\migrations\0008_inspection_fiche_generation_method.py", "Python", 12, 0, 0, 0, 1, 6, 19
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\migrations\__init__.py", "Python", 0, 0, 0, 0, 0, 1, 1
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\models.py", "Python", 58, 0, 0, 0, 1, 5, 64
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\serializers.py", "Python", 52, 0, 0, 0, 2, 9, 63
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\urls.py", "Python", 9, 0, 0, 0, 0, 4, 13
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\views.py", "Python", 1360, 0, 0, 0, 161, 178, 1699
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\manage.py", "Python", 15, 0, 0, 0, 3, 5, 23
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\package-lock.json", "JSON", 0, 0, 0, 268, 0, 1, 269
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\package.json", "JSON", 0, 0, 0, 5, 0, 1, 6
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\requirements.txt", "pip requirements", 0, 10, 0, 0, 0, 0, 10
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\test_db.py", "Python", 22, 0, 0, 0, 2, 3, 27
"Total", "-", 2344, 10, 18, 273, 296, 402, 3343
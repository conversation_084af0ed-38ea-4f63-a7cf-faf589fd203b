export const getFileUrl = (filePath) => {
  if (!filePath) return '';
  
  // Handle both relative and absolute URLs
  if (filePath.startsWith('http')) {
    return filePath;
  }
  
  // Ensure media URL is properly formatted
  const baseUrl = process.env.REACT_APP_API_URL || 'http://197.140.142.170';
  return `${baseUrl}${filePath.startsWith('/') ? '' : '/'}${filePath}`;
};

export const downloadFile = async (url, filename) => {
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': '*/*',
      },
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const blob = await response.blob();
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename || 'download';
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(downloadUrl);
  } catch (error) {
    console.error('Download failed:', error);
    // Fallback to direct link
    window.open(url, '_blank');
  }
};

export const previewFile = (fileUrl, fileName) => {
  const extension = fileName.split('.').pop().toLowerCase();
  
  // Check if it's an image
  if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension)) {
    return fileUrl;
  }
  
  // For PDFs and other files, return the URL for iframe or direct access
  return fileUrl;
};

# Generated by Django 5.1 on 2025-06-21 19:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inspection', '0003_auto_20250611_1602'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='inspection',
            name='marquage_us_cl',
        ),
        migrations.RemoveField(
            model_name='inspection',
            name='marquage_us_cl2',
        ),
        migrations.RemoveField(
            model_name='inspection',
            name='marquage_us_div',
        ),
        migrations.RemoveField(
            model_name='inspection',
            name='marquage_us_div2',
        ),
        migrations.RemoveField(
            model_name='inspection',
            name='marquage_us_gr',
        ),
        migrations.RemoveField(
            model_name='inspection',
            name='marquage_us_gr2',
        ),
        migrations.AddField(
            model_name='inspection',
            name='equipement_custom',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='inspection',
            name='marquage_us',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='inspection',
            name='niveau_1',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='inspection',
            name='niveau_2',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='inspection',
            name='niveau_3',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='inspection',
            name='unite_custom',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='inspection',
            name='action',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='inspection',
            name='observations',
            field=models.TextField(blank=True, null=True),
        ),
    ]

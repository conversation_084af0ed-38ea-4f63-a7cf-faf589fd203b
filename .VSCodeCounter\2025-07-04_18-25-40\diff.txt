Date : 2025-07-04 18:25:40
Directory : c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend
Total : 61 files,  17199 codes, -124 comments, -85 blanks, all 16990 lines

Languages
+------------------+------------+------------+------------+------------+------------+
| language         | files      | code       | comment    | blank      | total      |
+------------------+------------+------------+------------+------------+------------+
| JSON             |          5 |     16,649 |          0 |          1 |     16,650 |
| JavaScript JSX   |         14 |      2,818 |        149 |        272 |      3,239 |
| PostCSS          |          2 |         45 |          0 |          8 |         53 |
| Markdown         |          1 |         38 |          0 |         33 |         71 |
| HTML             |          1 |         20 |         23 |          1 |         44 |
| XML              |          1 |          1 |          0 |          0 |          1 |
| pip requirements |          1 |        -10 |          0 |          0 |        -10 |
| <PERSON> Script     |          1 |        -18 |         -6 |         -7 |        -31 |
| Python           |         35 |     -2,344 |       -290 |       -393 |     -3,027 |
+------------------+------------+------------+------------+------------+------------+

Directories
+-------------------------------------------------------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+
| path                                                                                                                                      | files      | code       | comment    | blank      | total      |
+-------------------------------------------------------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+
| .                                                                                                                                         |         61 |     17,199 |       -124 |        -85 |     16,990 |
| . (Files)                                                                                                                                 |          3 |     16,935 |          0 |         35 |     16,970 |
| ..                                                                                                                                        |         39 |     -2,645 |       -296 |       -402 |     -3,343 |
| ..\backend                                                                                                                                |         39 |     -2,645 |       -296 |       -402 |     -3,343 |
| ..\backend (Files)                                                                                                                        |          6 |       -338 |        -11 |        -17 |       -366 |
| ..\backend\authentication                                                                                                                 |         10 |       -205 |        -18 |        -63 |       -286 |
| ..\backend\authentication (Files)                                                                                                         |          8 |       -184 |        -17 |        -55 |       -256 |
| ..\backend\authentication\migrations                                                                                                      |          2 |        -21 |         -1 |         -8 |        -30 |
| ..\backend\config                                                                                                                         |          5 |       -163 |        -30 |        -26 |       -219 |
| ..\backend\einspection                                                                                                                    |          5 |       -137 |        -65 |        -50 |       -252 |
| ..\backend\inspection                                                                                                                     |         13 |     -1,802 |       -172 |       -246 |     -2,220 |
| ..\backend\inspection (Files)                                                                                                             |          4 |     -1,479 |       -164 |       -196 |     -1,839 |
| ..\backend\inspection\migrations                                                                                                          |          9 |       -323 |         -8 |        -50 |       -381 |
| public                                                                                                                                    |          2 |         45 |         23 |          2 |         70 |
| src                                                                                                                                       |         17 |      2,864 |        149 |        280 |      3,293 |
| src (Files)                                                                                                                               |          8 |        116 |          5 |         19 |        140 |
| src\components                                                                                                                            |          1 |          9 |          0 |          4 |         13 |
| src\pages                                                                                                                                 |          6 |      2,614 |        136 |        243 |      2,993 |
| src\pages (Files)                                                                                                                         |          4 |      2,350 |        124 |        219 |      2,693 |
| src\pages\global                                                                                                                          |          2 |        264 |         12 |         24 |        300 |
| src\services                                                                                                                              |          2 |        125 |          8 |         14 |        147 |
+-------------------------------------------------------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+

Files
+-------------------------------------------------------------------------------------------------------------------------------------------+------------------+------------+------------+------------+------------+
| filename                                                                                                                                  | language         | code       | comment    | blank      | total      |
+-------------------------------------------------------------------------------------------------------------------------------------------+------------------+------------+------------+------------+------------+
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\authentication\__init__.py                                                       | Python           |          0 |          0 |         -1 |         -1 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\authentication\admin.py                                                          | Python           |         -1 |         -1 |         -2 |         -4 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\authentication\apps.py                                                           | Python           |         -4 |          0 |         -3 |         -7 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\authentication\migrations\0001_initial.py                                        | Python           |        -21 |         -1 |         -7 |        -29 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\authentication\migrations\__init__.py                                            | Python           |          0 |          0 |         -1 |         -1 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\authentication\models.py                                                         | Python           |        -14 |         -1 |         -4 |        -19 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\authentication\serializers.py                                                    | Python           |        -27 |          0 |         -5 |        -32 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\authentication\tests.py                                                          | Python           |         -1 |         -1 |         -2 |         -4 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\authentication\urls.py                                                           | Python           |        -12 |          0 |         -1 |        -13 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\authentication\views.py                                                          | Python           |       -125 |        -14 |        -37 |       -176 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\build.sh                                                                         | Shell Script     |        -18 |         -6 |         -7 |        -31 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\config\__init__.py                                                               | Python           |          0 |         -1 |          0 |         -1 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\config\asgi.py                                                                   | Python           |         -4 |         -8 |         -4 |        -16 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\config\settings.py                                                               | Python           |       -144 |        -13 |        -18 |       -175 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\config\urls.py                                                                   | Python           |        -11 |          0 |         -1 |        -12 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\config\wsgi.py                                                                   | Python           |         -4 |         -8 |         -3 |        -15 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\einspection\__init__.py                                                          | Python           |          0 |          0 |         -1 |         -1 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\einspection\asgi.py                                                              | Python           |         -4 |         -8 |         -5 |        -17 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\einspection\settings.py                                                          | Python           |       -119 |        -33 |        -36 |       -188 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\einspection\urls.py                                                              | Python           |        -10 |        -16 |         -3 |        -29 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\einspection\wsgi.py                                                              | Python           |         -4 |         -8 |         -5 |        -17 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\migrations\0001_initial.py                                            | Python           |         -6 |         -1 |         -6 |        -13 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\migrations\0002_initial.py                                            | Python           |        -62 |         -1 |         -7 |        -70 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\migrations\0003_auto_20250611_1602.py                                 | Python           |         -7 |         -1 |         -6 |        -14 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\migrations\0004_remove_inspection_marquage_us_cl_and_more.py          | Python           |        -71 |         -1 |         -6 |        -78 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\migrations\0005_inspection_photo_equipement_and_more.py               | Python           |        -22 |         -1 |         -6 |        -29 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\migrations\0006_inspection_fiche_generation_method.py                 | Python           |        -12 |         -1 |         -6 |        -19 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\migrations\0007_remove_inspection_fiche_generation_method_and_more.py | Python           |       -131 |         -1 |         -6 |       -138 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\migrations\0008_inspection_fiche_generation_method.py                 | Python           |        -12 |         -1 |         -6 |        -19 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\migrations\__init__.py                                                | Python           |          0 |          0 |         -1 |         -1 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\models.py                                                             | Python           |        -58 |         -1 |         -5 |        -64 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\serializers.py                                                        | Python           |        -52 |         -2 |         -9 |        -63 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\urls.py                                                               | Python           |         -9 |          0 |         -4 |        -13 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\views.py                                                              | Python           |     -1,360 |       -161 |       -178 |     -1,699 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\manage.py                                                                        | Python           |        -15 |         -3 |         -5 |        -23 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\package-lock.json                                                                | JSON             |       -268 |          0 |         -1 |       -269 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\package.json                                                                     | JSON             |         -5 |          0 |         -1 |         -6 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\requirements.txt                                                                 | pip requirements |        -10 |          0 |          0 |        -10 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\test_db.py                                                                       | Python           |        -22 |         -2 |         -3 |        -27 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\README.md                                                                       | Markdown         |         38 |          0 |         33 |         71 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\package-lock.json                                                               | JSON             |     16,850 |          0 |          1 |     16,851 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\package.json                                                                    | JSON             |         47 |          0 |          1 |         48 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\public\index.html                                                               | HTML             |         20 |         23 |          1 |         44 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\public\manifest.json                                                            | JSON             |         25 |          0 |          1 |         26 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\App.css                                                                     | PostCSS          |         33 |          0 |          6 |         39 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\App.js                                                                      | JavaScript JSX   |         37 |          1 |          4 |         42 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\App.test.js                                                                 | JavaScript JSX   |          7 |          0 |          2 |          9 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\components\PrivateRoute.js                                                  | JavaScript JSX   |          9 |          0 |          4 |         13 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\index.css                                                                   | PostCSS          |         12 |          0 |          2 |         14 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\index.js                                                                    | JavaScript JSX   |         13 |          0 |          2 |         15 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\logo.svg                                                                    | XML              |          1 |          0 |          0 |          1 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\pages\DashboardPage.js                                                      | JavaScript JSX   |         20 |          0 |          2 |         22 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\pages\Documents.js                                                          | JavaScript JSX   |        479 |         23 |         41 |        543 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\pages\InspectionFormPage.js                                                 | JavaScript JSX   |      1,538 |         95 |        161 |      1,794 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\pages\LoginPage.js                                                          | JavaScript JSX   |        313 |          6 |         15 |        334 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\pages\global\Sidebar.jsx                                                    | JavaScript JSX   |        227 |         10 |         19 |        256 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\pages\global\Topbar.jsx                                                     | JavaScript JSX   |         37 |          2 |          5 |         44 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\reportWebVitals.js                                                          | JavaScript JSX   |         12 |          0 |          2 |         14 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\services\api.js                                                             | JavaScript JSX   |        103 |          7 |         13 |        123 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\services\mockApi.js                                                         | JavaScript JSX   |         22 |          1 |          1 |         24 |
| c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\setupTests.js                                                               | JavaScript JSX   |          1 |          4 |          1 |          6 |
| Total                                                                                                                                     |                  |     17,199 |       -124 |        -85 |     16,990 |
+-------------------------------------------------------------------------------------------------------------------------------------------+------------------+------------+------------+------------+------------+
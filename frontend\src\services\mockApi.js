// Mock API functions that will be replaced with real API calls later
export const mockLogin = (credentials) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({ 
        token: 'mock-token', 
        user: { email: credentials.email, name: 'Test User' } 
      });
    }, 500);
  });
};

export const mockSubmitInspection = (data) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({ 
        id: Math.floor(Math.random() * 1000),
        ...data,
        status: 'submitted',
        createdAt: new Date().toISOString()
      });
    }, 500);
  });
};
import psycopg2
import traceback

try:
    # First try connecting to the default postgres database
    conn = psycopg2.connect(
        dbname='postgres',
        user='postgres',
        password='ilhamou150',
        host='localhost',
        port='5432'
    )
    print("Successfully connected to postgres database!")
    
    # Check if einspection database exists
    cur = conn.cursor()
    cur.execute("SELECT 1 FROM pg_database WHERE datname = 'einspection'")
    if cur.fetchone():
        print("einspection database exists!")
    else:
        print("einspection database does not exist!")
    
    cur.close()
    conn.close()
except Exception as e:
    print("Error connecting to the database:")
    traceback.print_exc() 
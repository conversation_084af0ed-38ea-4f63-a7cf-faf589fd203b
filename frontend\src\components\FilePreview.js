import React from 'react';
import { Box, Typography, Card, CardMedia, CardContent, Button } from '@mui/material';
import { getFileUrl } from '../utils/fileUtils';

const FilePreview = ({ file }) => {
  const fileUrl = getFileUrl(file.file);
  const fileExtension = file.name.split('.').pop().toLowerCase();

  const isImage = ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(fileExtension);
  const isPdf = fileExtension === 'pdf';

  if (isImage) {
    return (
      <Box sx={{ maxWidth: 400, margin: 'auto' }}>
        <Card>
          <CardMedia
            component="img"
            height="300"
            image={fileUrl}
            alt={file.name}
            onError={(e) => {
              console.error('Image load error:', e);
              e.target.src = '/placeholder-image.png';
            }}
          />
          <CardContent>
            <Typography variant="body2" color="text.secondary">
              {file.name}
            </Typography>
          </CardContent>
        </Card>
      </Box>
    );
  }

  if (isPdf) {
    return (
      <Box sx={{ maxWidth: 400, margin: 'auto' }}>
        <Card>
          <CardContent>
            <Typography variant="h6" component="div">
              PDF Document
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {file.name}
            </Typography>
            <Box sx={{ mt: 2 }}>
              <iframe
                src={`${fileUrl}#toolbar=0`}
                width="100%"
                height="400"
                title={file.name}
                style={{ border: 'none' }}
              />
            </Box>
          </CardContent>
        </Card>
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="body1">
        File: {file.name}
      </Typography>
      <Typography variant="body2" color="text.secondary">
        Type: {fileExtension.toUpperCase()}
      </Typography>
    </Box>
  );
};

export default FilePreview;

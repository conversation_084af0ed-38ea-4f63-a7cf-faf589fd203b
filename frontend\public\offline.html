<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-Inspection - Hors ligne</title>
    <style>
        body {
            font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            text-align: center;
            max-width: 500px;
            padding: 2rem;
        }
        .icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.8;
        }
        h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            font-weight: 300;
        }
        p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.6;
        }
        .features {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
            text-align: left;
        }
        .features h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        .features ul {
            list-style: none;
            padding: 0;
        }
        .features li {
            margin: 0.5rem 0;
            padding-left: 1.5rem;
            position: relative;
        }
        .features li:before {
            content: "✓";
            color: #4CAF50;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
        .retry-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: background 0.3s;
        }
        .retry-btn:hover {
            background: #45a049;
        }
        .status {
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">📱</div>
        <h1>E-Inspection</h1>
        <p>Vous êtes actuellement hors ligne</p>

        <div class="features">
            <h3>Fonctionnalités disponibles hors ligne :</h3>
            <ul>
                <li>Consulter les inspections sauvegardées</li>
                <li>Naviguer dans les documents de référence</li>
                <li>Ajouter de nouvelles données (synchronisation automatique)</li>
                <li>Télécharger des fichiers (lorsque reconnecté)</li>
            </ul>
        </div>

        <p>
            Reconnectez-vous à internet pour synchroniser vos données et accéder à toutes les fonctionnalités.
        </p>

        <button class="retry-btn" onclick="window.location.reload()">
            Réessayer la connexion
        </button>

        <div class="status">
            <p><strong>Statut :</strong> Hors ligne</p>
            <p>Les données seront automatiquement synchronisées lors de la reconnexion.</p>
        </div>
    </div>

    <script>
        // Check network status periodically
        setInterval(() => {
            if (navigator.onLine) {
                // Try to reload the page when back online
                window.location.reload();
            }
        }, 5000);

        // Listen for online event
        window.addEventListener('online', () => {
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        });
    </script>
</body>
</html>
import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Grid,
  Paper,
  Box,
  Card,
  CardContent,
  CardActions,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Chip,
  Alert,
  Tooltip,
  Fab,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Snackbar,
  CircularProgress
} from '@mui/material';
import {
  FolderOpen as FolderIcon,
  Upload as UploadIcon,
  Description as DocumentIcon,
  PictureAsPdf as PdfIcon,
  Image as ImageIcon,
  TableChart as ExcelIcon,
  Article as WordIcon,
  Engineering as DwgIcon,
  Info as InfoIcon,
  Add as AddIcon,
  CloudUpload as CloudUploadIcon,
  Download as DownloadIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Visibility as PreviewIcon,
  Close as CloseIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit as FullscreenExitIcon
} from '@mui/icons-material';
import { fetchUserFiles, uploadUserFiles, deleteUserFile, fetchUserFilesWithOffline, uploadUserFilesWithOffline, deleteUserFileWithOffline } from '../services/api';

// PDF Preview Component with authentication
const PDFPreview = ({ file, isFullscreen }) => {
  const [pdfUrl, setPdfUrl] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadPDF = async () => {
      try {
        setLoading(true);
        console.log('Loading PDF from:', file.fileUrl);

        // Check if we're offline
        const isOnline = navigator.onLine;
        if (!isOnline) {
          throw new Error('Aperçu PDF non disponible hors ligne. Reconnectez-vous pour voir les PDFs.');
        }

        const response = await fetch(file.fileUrl, {
          method: 'GET',
          headers: {
            'Authorization': `Token ${localStorage.getItem('authToken')}`,
          },
          credentials: 'include'
        });

        console.log('PDF fetch response:', response.status, response.statusText);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
        }

        // Check content type
        const contentType = response.headers.get('content-type');
        console.log('PDF content type:', contentType);

        if (!contentType || !contentType.includes('application/pdf')) {
          // Log the actual HTML response to see what we're getting
          const text = await response.text();
          console.log('HTML response received instead of PDF:', text.substring(0, 500));
          throw new Error(`Expected PDF but got: ${contentType}`);
        }

        const blob = await response.blob();
        console.log('PDF blob size:', blob.size, 'type:', blob.type);

        if (blob.size === 0) {
          throw new Error('Received empty file');
        }

        const url = window.URL.createObjectURL(blob);
        setPdfUrl(url);
        setError(null);
        console.log('PDF blob URL created:', url);
      } catch (err) {
        console.error('Error loading PDF:', err);
        setError(`Erreur lors du chargement du PDF: ${err.message}`);
      } finally {
        setLoading(false);
      }
    };

    loadPDF();

    // Cleanup function to revoke the object URL
    return () => {
      if (pdfUrl) {
        window.URL.revokeObjectURL(pdfUrl);
      }
    };
  }, [file.fileUrl]);

  if (loading) {
    return (
      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: isFullscreen ? '90vh' : '70vh'
      }}>
        <CircularProgress />
        <Typography variant="body2" sx={{ ml: 2 }}>
          Chargement du PDF...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: isFullscreen ? '90vh' : '70vh',
        flexDirection: 'column'
      }}>
        <Typography variant="h6" color="error" gutterBottom>
          {error}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Impossible de charger le fichier PDF
        </Typography>
        <Button
          variant="outlined"
          onClick={() => {
            // Try to open the file directly with authentication
            const newWindow = window.open('', '_blank');
            fetch(file.fileUrl, {
              headers: {
                'Authorization': `Token ${localStorage.getItem('authToken')}`,
              }
            })
            .then(response => response.blob())
            .then(blob => {
              const url = window.URL.createObjectURL(blob);
              newWindow.location.href = url;
            })
            .catch(err => {
              console.error('Failed to open file:', err);
              newWindow.close();
              alert('Impossible d\'ouvrir le fichier');
            });
          }}
          sx={{ mt: 2 }}
        >
          Ouvrir dans un nouvel onglet
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%', height: isFullscreen ? '90vh' : '70vh' }}>
      <iframe
        src={`${pdfUrl}#toolbar=0&navpanes=0&scrollbar=0`}
        width="100%"
        height="100%"
        style={{ border: 'none' }}
        title={`Aperçu de ${file.name}`}
        onLoad={() => console.log('PDF iframe loaded successfully')}
        onError={(e) => console.error('PDF iframe error:', e)}
      />
    </Box>
  );
};

// Image Preview Component with authentication
const ImagePreview = ({ file, isFullscreen }) => {
  const [imageUrl, setImageUrl] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadImage = async () => {
      try {
        setLoading(true);
        console.log('Loading image from:', file.fileUrl);

        // Check if we're offline
        const isOnline = navigator.onLine;
        if (!isOnline) {
          throw new Error('Aperçu image non disponible hors ligne. Reconnectez-vous pour voir les images.');
        }

        const response = await fetch(file.fileUrl, {
          method: 'GET',
          headers: {
            'Authorization': `Token ${localStorage.getItem('authToken')}`,
          },
          credentials: 'include'
        });

        console.log('Image fetch response:', response.status, response.statusText);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
        }

        const contentType = response.headers.get('content-type');
        console.log('Image content type:', contentType);

        if (!contentType || !contentType.startsWith('image/')) {
          throw new Error(`Expected image but got: ${contentType}`);
        }

        const blob = await response.blob();
        console.log('Image blob size:', blob.size, 'type:', blob.type);

        if (blob.size === 0) {
          throw new Error('Received empty file');
        }

        const url = window.URL.createObjectURL(blob);
        setImageUrl(url);
        setError(null);
        console.log('Image blob URL created:', url);
      } catch (err) {
        console.error('Error loading image:', err);
        setError(`Erreur lors du chargement de l'image: ${err.message}`);
      } finally {
        setLoading(false);
      }
    };

    loadImage();

    // Cleanup function to revoke the object URL
    return () => {
      if (imageUrl) {
        window.URL.revokeObjectURL(imageUrl);
      }
    };
  }, [file.fileUrl]);

  if (loading) {
    return (
      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: isFullscreen ? '90vh' : '70vh'
      }}>
        <CircularProgress />
        <Typography variant="body2" sx={{ ml: 2 }}>
          Chargement de l'image...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: isFullscreen ? '90vh' : '70vh',
        flexDirection: 'column'
      }}>
        <Typography variant="h6" color="error" gutterBottom>
          {error}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Impossible de charger l'image
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ textAlign: 'center', maxHeight: isFullscreen ? '90vh' : '70vh', overflow: 'auto' }}>
      <img
        src={imageUrl}
        alt={file.name}
        style={{
          maxWidth: '100%',
          maxHeight: '100%',
          objectFit: 'contain'
        }}
        onLoad={() => console.log('Image loaded successfully')}
        onError={(e) => console.error('Image display error:', e)}
      />
    </Box>
  );
};

const ReferenceDocumentsPage = () => {
  const [selectedFolder, setSelectedFolder] = useState(null);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [files, setFiles] = useState({});
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false);
  const [previewFile, setPreviewFile] = useState(null);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Load files from backend API on component mount and when selectedFolder changes
  useEffect(() => {
    const loadFiles = async () => {
      if (!selectedFolder) {
        setFiles({});
        return;
      }
      try {
        const data = await fetchUserFilesWithOffline(selectedFolder.id);
        // Convert uploadDate strings to Date objects
        const apiBase = (process.env.REACT_APP_API_URL) || 'http://197.140.142.170';
        const filesByFolder = {};
        filesByFolder[selectedFolder.id] = data.map(file => {
          // Prefer API content endpoint; fall back to provided URL
          const apiContentUrl = `${apiBase}/api/inspections/userfiles/${file.id}/content/`;
          const providedUrl = file.file;
          const finalUrl = apiContentUrl; // Always use the direct /media/ URL
          return {
            id: file.id,
            name: file.name,
            size: file.size,
            uploadDate: new Date(file.upload_date),
            type: file.type || file.name.split('.').pop().toLowerCase(),
            fileUrl: finalUrl
          };
        });
        setFiles(filesByFolder);
      } catch (error) {
        console.error('Error loading files from backend:', error);
        setSnackbarMessage("Erreur lors du chargement des fichiers");
        setSnackbarOpen(true);
      }
    };
    loadFiles();
  }, [selectedFolder]); // Line 247 fix: Ensure selectedFolder is in dependency array if used inside

  // Configuration des dossiers avec descriptions détaillées
  const folders = [
    {
      id: 'zonage',
      title: 'Plans et rapports de zonage',
      icon: <FolderIcon sx={{ fontSize: 48, color: '#1976d2' }} />,
      description: 'Centralisation des documents de zonage ATEX',
      detailedDescription: `Ce dossier regroupe tous les documents relatifs au zonage ATEX de votre installation :
• Plans de zonage ATEX (PDF, DWG, AutoCAD)
• Rapports d'analyse de risques d'explosion
• Études de classification des zones dangereuses
• Documents de révision et mise à jour du zonage
• Cartographies des zones Ex (0, 1, 2 pour gaz / 20, 21, 22 pour poussières)
Ces documents constituent la base réglementaire pour déterminer les exigences applicables aux équipements installés dans chaque zone.`,
      acceptedFormats: ['PDF', 'DWG', 'AutoCAD', 'PNG', 'JPG'],
      color: '#e3f2fd'
    },
    {
      id: 'inventaire',
      title: 'Inventaire des équipements ATEX',
      icon: <FolderIcon sx={{ fontSize: 48, color: '#388e3c' }} />,
      description: 'Centralisation de l\'inventaire des équipements ATEX',
      detailedDescription: `Ce dossier centralise l'inventaire complet des équipements installés en zones ATEX :
• Listes d'équipements par zone et par unité
• Fiches techniques des équipements ATEX
• Registres de maintenance et d'inspection
• Historiques des modifications d'équipements
• Tableaux de correspondance équipement/zone/protection
Cet inventaire permet de s'assurer que chaque équipement est adapté à sa zone d'installation et facilite la planification des inspections.`,
      acceptedFormats: ['Excel', 'PDF', 'Word', 'CSV'],
      color: '#e8f5e8'
    },
    {
      id: 'referentiels',
      title: 'Référentiels d\'inspection',
      icon: <FolderIcon sx={{ fontSize: 48, color: '#f57c00' }} />,
      description: 'Documents de base pour les campagnes d\'inspection',
      detailedDescription: `Ce dossier contient tous les référentiels utilisés lors des inspections ATEX :
• Normes applicables (IEC 60079, EN 60079, etc.)
• Procédures internes d'inspection
• Grilles de contrôle et check-lists
• Guides d'inspection par type d'équipement
• Critères d'acceptation et de refus
• Modèles de rapports d'inspection
Ces documents garantissent la cohérence et la qualité des inspections réalisées.`,
      acceptedFormats: ['PDF', 'Word', 'Excel'],
      color: '#fff3e0'
    },
    {
      id: 'techniques',
      title: 'Documents techniques',
      icon: <FolderIcon sx={{ fontSize: 48, color: '#7b1fa2' }} />,
      description: 'Documentation technique et certificats',
      detailedDescription: `Ce dossier regroupe l'ensemble de la documentation technique des équipements :
• Manuels d'utilisation et notices techniques
• Schémas électriques et plans d'installation
• Certificats de conformité ATEX (Ex-certificates)
• Déclarations CE de conformité
• Rapports d'essais et de certification
• Documentation des modifications techniques
• Fiches de données de sécurité (FDS)
Cette documentation est essentielle pour la vérification réglementaire et la maintenance des équipements ATEX.`,
      acceptedFormats: ['PDF', 'Word', 'Excel', 'PNG', 'JPG', 'DWG'],
      color: '#f3e5f5'
    }
  ];

  // Fonction pour obtenir l'icône selon le type de fichier
  const getFileIcon = (fileName) => {
    const extension = fileName.split('.').pop().toLowerCase();
    switch (extension) {
      case 'pdf':
        return <PdfIcon sx={{ color: '#d32f2f', fontSize: 48 }} />;
      case 'doc':
      case 'docx':
        return <WordIcon sx={{ color: '#1976d2', fontSize: 48 }} />;
      case 'xls':
      case 'xlsx':
      case 'csv':
        return <ExcelIcon sx={{ color: '#388e3c', fontSize: 48 }} />;
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
      case 'bmp':
      case 'webp':
      case 'svg':
        return <ImageIcon sx={{ color: '#f57c00', fontSize: 48 }} />;
      case 'dwg':
      case 'dxf':
        return <DwgIcon sx={{ color: '#7b1fa2', fontSize: 48 }} />;
      case 'txt':
        return <DocumentIcon sx={{ color: '#616161', fontSize: 48 }} />;
      case 'mp4':
      case 'webm':
      case 'ogg':
      case 'avi':
      case 'mov':
        return <DocumentIcon sx={{ color: '#e91e63', fontSize: 48 }} />;
      case 'mp3':
      case 'wav':
      case 'flac':
        return <DocumentIcon sx={{ color: '#9c27b0', fontSize: 48 }} />;
      default:
        return <DocumentIcon sx={{ color: '#616161', fontSize: 48 }} />;
    }
  };

  const handleFolderClick = (folder) => {
    setSelectedFolder(folder);
  };

  const handleUpload = (folderId) => {
    // Find the folder object based on ID
    const folder = folders.find(f => f.id === folderId);
    if (folder) {
      setSelectedFolder(folder); // Line 244 fix: Ensure setSelectedFolder receives a valid folder object
    }
    setUploadDialogOpen(true);
  };

  const handleFileUpload = async (event) => {
    const uploadedFiles = Array.from(event.target.files);
    if (selectedFolder && uploadedFiles.length > 0) {
      try {
        const response = await uploadUserFilesWithOffline(selectedFolder.id, uploadedFiles); // Line 482 fix: Ensure uploadUserFiles is defined and awaited correctly

        // Handle offline response
        if (response.queued) {
          setSnackbarMessage(`Fichier mis en file d'attente pour téléchargement en ligne`);
          setSnackbarOpen(true);
          setUploadDialogOpen(false);
          return;
        }

        // Update files state with newly uploaded files
        const newFiles = { ...files };
        if (!newFiles[selectedFolder.id]) {
          newFiles[selectedFolder.id] = [];
        }
        // Append uploaded files from response
        response.forEach(file => {
          newFiles[selectedFolder.id].push({
            id: file.id,
            name: file.name,
            size: file.size,
            uploadDate: new Date(file.upload_date),
            type: file.type || file.name.split('.').pop().toLowerCase(), // Use file extension if type is missing
            fileUrl: file.file // Backend returns full URL
          });
        });
        setFiles(newFiles);
        setUploadDialogOpen(false);
        setSnackbarMessage(`${uploadedFiles.length} fichier(s) ajouté(s) avec succès`);
        setSnackbarOpen(true);
      } catch (error) {
        console.error('Error uploading files:', error);
        setSnackbarMessage("Erreur lors de l'upload des fichiers");
        setSnackbarOpen(true);
      }
    }
  };

  const handleFileMenuOpen = (event, file) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setSelectedFile(file);
  };

  const handleFileMenuClose = () => {
    setAnchorEl(null);
    setSelectedFile(null);
  };

  const handleDownloadFile = async () => {
    if (selectedFile && selectedFile.fileUrl) {
      console.log('Downloading file:', selectedFile.name, 'URL:', selectedFile.fileUrl);

      // Check if we're offline
      const isOnline = navigator.onLine;

      if (!isOnline) {
        setSnackbarMessage('Téléchargement non disponible hors ligne. Reconnectez-vous pour télécharger des fichiers.');
        setSnackbarOpen(true);
        handleFileMenuClose();
        return;
      }

      try {
        // Fetch the file with authentication
        const response = await fetch(selectedFile.fileUrl, {
          method: 'GET',
          headers: {
            'Authorization': `Token ${localStorage.getItem('authToken')}`,
          },
          credentials: 'include'
        });

        console.log('Download response:', response.status, response.statusText);
        console.log('Content-Type:', response.headers.get('content-type'));
        console.log('Content-Length:', response.headers.get('content-length'));

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
        }

        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('text/html')) {
          throw new Error('Server returned HTML instead of file - authentication issue');
        }

        // Get the blob with proper content type
        const blob = await response.blob();
        console.log('Download blob size:', blob.size, 'type:', blob.type);

        if (blob.size === 0) {
          throw new Error('Received empty file');
        }

        // Create download link
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = selectedFile.name;

        // Ensure the link is added to DOM for Firefox compatibility
        document.body.appendChild(link);
        link.click();

        // Clean up
        setTimeout(() => {
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);
        }, 100);

        setSnackbarMessage(`Fichier "${selectedFile.name}" téléchargé avec succès`);
        setSnackbarOpen(true);

      } catch (error) {
        console.error('File download failed:', error);
        setSnackbarMessage(`Erreur lors du téléchargement: ${error.message}`);
        setSnackbarOpen(true);
      }
    } else {
      setSnackbarMessage(`Impossible de télécharger "${selectedFile?.name}" - fichier non disponible`);
      setSnackbarOpen(true);
    }
    handleFileMenuClose();
  };

  const handleDeleteFile = async () => {
    if (selectedFile && selectedFolder) {
      try {
        // Call the API to delete the file on the server with offline support
        await deleteUserFileWithOffline(selectedFile.id);

        // Update the UI after successful deletion
        const newFiles = { ...files };
        newFiles[selectedFolder.id] = newFiles[selectedFolder.id].filter(
          file => file.id !== selectedFile.id
        );
        setFiles(newFiles);
        setSnackbarMessage(`Fichier "${selectedFile.name}" supprimé avec succès`);
        setSnackbarOpen(true);
      } catch (error) {
        console.error('Error deleting file:', error);
        setSnackbarMessage(`Erreur lors de la suppression du fichier "${selectedFile.name}"`);
        setSnackbarOpen(true);
      }
    }
    handleFileMenuClose();
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  const testMediaAccess = async () => {
    try {
      const response = await fetch('http://197.140.142.170/api/inspections/test/media/', {
        headers: {
          'Authorization': `Token ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json',
        },
      });
      const data = await response.json();
      console.log('Media test response:', data);
      setSnackbarMessage('Check console for media test results');
      setSnackbarOpen(true);
    } catch (error) {
      console.error('Media test failed:', error);
      setSnackbarMessage('Media test failed - check console');
      setSnackbarOpen(true);
    }
  };

  const testUrlRouting = async () => {
    try {
      // Test the simple media test endpoint
      const response = await fetch('http://197.140.142.170/media/test/', {
        headers: {
          'Authorization': `Token ${localStorage.getItem('authToken')}`,
        },
      });
      const text = await response.text();
      console.log('URL routing test response:', text);
      setSnackbarMessage(`URL routing test: ${text}`);
      setSnackbarOpen(true);
    } catch (error) {
      console.error('URL routing test failed:', error);
      setSnackbarMessage('URL routing test failed - check console');
      setSnackbarOpen(true);
    }
  };

  const testWhichProject = async () => {
    try {
      // Test which Django project is running
      console.log('Testing which Django project is running...');

      // Test einspection project (root endpoint)
      const einspectionResponse = await fetch('http://197.140.142.170/');
      const einspectionData = await einspectionResponse.json();
      console.log('Root endpoint response:', einspectionData);

      // Test config project health endpoint
      try {
        const configResponse = await fetch('http://197.140.142.170/health/');
        const configData = await configResponse.json();
        console.log('Config health endpoint response:', configData);
      } catch (configError) {
        console.log('Config health endpoint not accessible:', configError.message);
      }

      setSnackbarMessage('Check console for project detection results');
      setSnackbarOpen(true);
    } catch (error) {
      console.error('Project detection test failed:', error);
      setSnackbarMessage('Project detection test failed - check console');
      setSnackbarOpen(true);
    }
  };

  const handlePreviewFile = () => {
    if (selectedFile) {
      setPreviewFile(selectedFile);
      setPreviewDialogOpen(true);
    }
    handleFileMenuClose();
  };

  const handleClosePreview = () => {
    setPreviewDialogOpen(false);
    setPreviewFile(null);
    setIsFullscreen(false);
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const getPreviewContent = (file) => {
    if (!file || !file.fileUrl) {
      return (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h6" color="text.secondary">
            Aperçu non disponible
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Le fichier ne peut pas être prévisualisé
          </Typography>
        </Box>
      );
    }

    const extension = file.name.split('.').pop().toLowerCase();
    console.log('File preview - Name:', file.name, 'Extension:', extension, 'URL:', file.fileUrl);

    switch (extension) {
      case 'pdf':
        return <PDFPreview file={file} isFullscreen={isFullscreen} />;

      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
      case 'bmp':
      case 'webp':
      case 'svg':
        return <ImagePreview file={file} isFullscreen={isFullscreen} />;

      default:
        return (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Box sx={{ mb: 2 }}>
              {getFileIcon(file.name)}
            </Box>
            <Typography variant="h6" gutterBottom>
              {file.name}
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Aperçu non disponible pour ce type de fichier
            </Typography>
            <Typography variant="caption" color="text.secondary" display="block" sx={{ mb: 2 }}>
              Taille: {(file.size / 1024 / 1024).toFixed(2)} MB
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Téléchargez le fichier pour l'ouvrir dans l'application appropriée
            </Typography>
            <Button
              variant="contained"
              startIcon={<DownloadIcon />}
              onClick={async () => {
                try {
                  console.log('Downloading from preview:', file.name);
                  const response = await fetch(file.fileUrl, {
                    method: 'GET',
                    headers: {
                      'Authorization': `Token ${localStorage.getItem('authToken')}`,
                    },
                    credentials: 'include'
                  });

                  if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                  }

                  const blob = await response.blob();
                  console.log('Preview download blob size:', blob.size);

                  if (blob.size === 0) {
                    throw new Error('Received empty file');
                  }

                  const url = window.URL.createObjectURL(blob);
                  const link = document.createElement('a');
                  link.href = url;
                  link.download = file.name;
                  document.body.appendChild(link);
                  link.click();

                  setTimeout(() => {
                    document.body.removeChild(link);
                    window.URL.revokeObjectURL(url);
                  }, 100);
                } catch (error) {
                  console.error('Preview download failed:', error);
                  alert(`Erreur de téléchargement: ${error.message}`);
                }
              }}
              sx={{ textTransform: 'none' }}
            >
              Télécharger le fichier
            </Button>
          </Box>
        );
    }
  };

  return (
    <Box
      sx={{
        backgroundColor: '#ffffff',
        minHeight: '100vh',
        ml: '280px',
        pt: 2,
        pb: 4,
        px: 3,
        boxSizing: 'border-box'
      }}
    >
      <Container maxWidth="xl" sx={{ position: 'relative', top: '-600px', zIndex: 1 }}> {/* Line 550 fix: Removed problematic top style */}
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mb: 4 }}>
          <Typography variant="h3" component="h1" sx={{ color: '#1976d2' }}>
            Documents de référence
          </Typography>
         
          
          
        </Box>
        <Alert severity="info" sx={{ mb: 4 }}>
          <Typography variant="body2">
            📁 Organisez vos documents ATEX par catégorie pour un accès rapide et une meilleure traçabilité. 
            Chaque dossier a une fonction spécifique dans la gestion de vos équipements ATEX.
          </Typography>
        </Alert>
        {!selectedFolder ? (
          // Vue des dossiers
          <Grid container spacing={3} justifyContent="center">
            {folders.map((folder) => (
              <Grid item xs={12} md={6} lg={5} xl={4} key={folder.id}>
                <Card 
                  sx={{ 
                    height: '100%', 
                    cursor: 'pointer',
                    transition: 'transform 0.2s, box-shadow 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: 4
                    },
                    bgcolor: folder.color
                  }}
                  onClick={() => handleFolderClick(folder)}
                >
                  <CardContent sx={{ textAlign: 'center', pb: 1 }}>
                    <Box sx={{ mb: 2 }}>
                      {folder.icon}
                    </Box>
                    <Typography variant="h5" component="h2" gutterBottom sx={{ fontWeight: 'bold' }}>
                      {folder.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {folder.description}
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, justifyContent: 'center' }}>
                      {folder.acceptedFormats.map((format) => (
                        <Chip 
                          key={format} 
                          label={format} 
                          size="small" 
                          variant="outlined"
                          sx={{ fontSize: '0.75rem' }}
                        />
                      ))}
                    </Box>
                  </CardContent>
                  <CardActions sx={{ justifyContent: 'center', pt: 0 }}>
                    <Button 
                      size="small" 
                      startIcon={<InfoIcon />}
                      sx={{ textTransform: 'none' }}
                    >
                      Voir les détails
                    </Button>
                    <Button 
                      size="small" 
                      startIcon={<UploadIcon />}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleUpload(folder.id);
                      }}
                      sx={{ textTransform: 'none' }}
                    >
                      Ajouter fichiers
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        ) : (
          // Vue détaillée d'un dossier
          <Box>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <Button 
                onClick={() => setSelectedFolder(null)}
                sx={{ mr: 2 }}
              >
                ← Retour aux dossiers
              </Button>
              <Typography variant="h4" sx={{ flexGrow: 1, color: '#1976d2' }}>
                {selectedFolder.title}
              </Typography>
              <Button
                variant="contained"
                startIcon={<UploadIcon />}
                onClick={() => setUploadDialogOpen(true)}
                sx={{ textTransform: 'none' }}
              >
                Ajouter des fichiers
              </Button>
            </Box>
            <Paper sx={{ p: 3, mb: 3, bgcolor: selectedFolder.color }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <InfoIcon sx={{ mr: 1 }} />
                Guide d'utilisation de ce dossier
              </Typography>
              <Typography variant="body1" sx={{ whiteSpace: 'pre-line', lineHeight: 1.6 }}>
                {selectedFolder.detailedDescription}
              </Typography>
            </Paper>
            {/* Liste des fichiers */}
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Fichiers ({files[selectedFolder.id]?.length || 0})
              </Typography>
              {files[selectedFolder.id]?.length > 0 ? (
                <Grid container spacing={2}>
                  {files[selectedFolder.id].map((file) => (
                    <Grid item xs={12} sm={6} md={4} key={file.id}>
                      <Card sx={{
                        height: '100%',
                        position: 'relative',
                        '&:hover .file-menu-button': {
                          opacity: 1
                        }
                      }}>
                        <CardContent sx={{ textAlign: 'center', pb: 1 }}>
                          <Box sx={{ display: 'flex', justifyContent: 'flex-end', position: 'absolute', top: 8, right: 8 }}>
                            <IconButton
                              className="file-menu-button"
                              size="small"
                              onClick={(e) => handleFileMenuOpen(e, file)}
                              sx={{
                                opacity: 0,
                                transition: 'opacity 0.2s',
                                bgcolor: 'rgba(255,255,255,0.8)',
                                '&:hover': {
                                  bgcolor: 'rgba(255,255,255,0.9)'
                                }
                              }}
                            >
                              <MoreVertIcon fontSize="small" />
                            </IconButton>
                          </Box>
                          <Box sx={{ mb: 1, mt: 2 }}>
                            {getFileIcon(file.name)}
                          </Box>
                          <Typography variant="body2" noWrap title={file.name}>
                            {file.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {(file.size / 1024 / 1024).toFixed(2)} MB
                          </Typography>
                          <Typography variant="caption" color="text.secondary" display="block">
                            {file.uploadDate.toLocaleDateString()}
                          </Typography>
                        </CardContent>
                        <CardActions sx={{ justifyContent: 'center', pt: 0 }}>
                          <Button
                            size="small"
                            startIcon={<PreviewIcon />}
                            onClick={() => {
                              setPreviewFile(file);
                              setPreviewDialogOpen(true);
                            }}
                            disabled={!file.fileUrl}
                            sx={{ textTransform: 'none' }}
                          >
                            Aperçu
                          </Button>
                        </CardActions>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              ) : (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <CloudUploadIcon sx={{ fontSize: 64, color: '#ccc', mb: 2 }} />
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    Aucun fichier dans ce dossier
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Commencez par ajouter vos premiers documents
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<UploadIcon />}
                    onClick={() => setUploadDialogOpen(true)}
                    sx={{ textTransform: 'none' }}
                  >
                    Ajouter des fichiers
                  </Button>
                </Box>
              )}
            </Paper>
          </Box>
        )}
        {/* Dialog d'upload */}
        <Dialog open={uploadDialogOpen} onClose={() => setUploadDialogOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>
            Ajouter des fichiers - {selectedFolder?.title}
          </DialogTitle>
          <DialogContent>
            <Box sx={{ textAlign: 'center', py: 3 }}>
              <input
                type="file"
                multiple
                onChange={handleFileUpload}
                style={{ display: 'none' }}
                id="file-upload"
                // Note: accept attribute format might need adjustment based on actual file types
                accept={selectedFolder?.acceptedFormats?.map(f => {
                  // Map common format names to MIME types or extensions
                  switch(f.toLowerCase()) {
                    case 'pdf': return '.pdf,application/pdf';
                    case 'png': return '.png,image/png';
                    case 'jpg': case 'jpeg': return '.jpg,.jpeg,image/jpeg';
                    case 'doc': return '.doc,application/msword';
                    case 'docx': return '.docx,application/vnd.openxmlformats-officedocument.wordprocessingml.document';
                    case 'xls': return '.xls,application/vnd.ms-excel';
                    case 'xlsx': return '.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                    case 'csv': return '.csv,text/csv';
                    case 'dwg': return '.dwg'; // MIME type for DWG is not standard
                    case 'dxf': return '.dxf'; // MIME type for DXF is not standard
                    case 'autocad': return '.dwg,.dxf'; // Assume AutoCAD refers to DWG/DXF
                    default: return `.${f.toLowerCase()}`;
                  }
                }).join(',')}
              />
              <label htmlFor="file-upload">
                <Button
                  variant="outlined"
                  component="span"
                  startIcon={<UploadIcon />}
                  sx={{ mb: 2, textTransform: 'none' }}
                >
                  Sélectionner des fichiers
                </Button>
              </label>
              <Typography variant="body2" color="text.secondary">
                Formats acceptés: {selectedFolder?.acceptedFormats?.join(', ')}
              </Typography>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setUploadDialogOpen(false)}>Annuler</Button>
          </DialogActions>
        </Dialog>
        {/* File Context Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleFileMenuClose}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'right',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
        >
          <MenuItem
            onClick={handlePreviewFile}
            disabled={!selectedFile?.fileUrl}
          >
            <ListItemIcon>
              <PreviewIcon fontSize="small" sx={{ color: selectedFile?.fileUrl ? 'inherit' : 'text.disabled' }} />
            </ListItemIcon>
            <ListItemText>
              {selectedFile?.fileUrl ? 'Aperçu' : 'Aperçu (non disponible)'}
            </ListItemText>
          </MenuItem>
          <MenuItem
            onClick={handleDownloadFile}
            disabled={!selectedFile?.fileUrl}
          >
            <ListItemIcon>
              <DownloadIcon fontSize="small" sx={{ color: selectedFile?.fileUrl ? 'inherit' : 'text.disabled' }} />
            </ListItemIcon>
            <ListItemText>
              {selectedFile?.fileUrl ? 'Télécharger' : 'Télécharger (non disponible)'}
            </ListItemText>
          </MenuItem>
          <MenuItem onClick={handleDeleteFile} sx={{ color: 'error.main' }}>
            <ListItemIcon>
              <DeleteIcon fontSize="small" sx={{ color: 'error.main' }} />
            </ListItemIcon>
            <ListItemText>Supprimer</ListItemText>
          </MenuItem>
        </Menu>
        {/* Preview Dialog */}
        <Dialog
          open={previewDialogOpen}
          onClose={handleClosePreview}
          maxWidth={isFullscreen ? false : "lg"}
          fullWidth={!isFullscreen}
          fullScreen={isFullscreen}
          PaperProps={{
            sx: {
              ...(isFullscreen && {
                margin: 0,
                maxHeight: '100vh',
                maxWidth: '100vw'
              })
            }
          }}
        >
          <DialogTitle sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            pb: 1
          }}>
            <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
              Aperçu: {previewFile?.name}
            </Typography>
            <Box>
              <IconButton
                onClick={toggleFullscreen}
                sx={{ mr: 1 }}
                title={isFullscreen ? "Quitter le plein écran" : "Plein écran"}
              >
                {isFullscreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
              </IconButton>
              <IconButton onClick={handleClosePreview} title="Fermer">
                <CloseIcon />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent sx={{ p: 0, overflow: 'hidden' }}>
            {previewFile && getPreviewContent(previewFile)}
          </DialogContent>
          {!isFullscreen && (
            <DialogActions>
              <Button
                startIcon={<DownloadIcon />}
                onClick={async () => {
                  if (previewFile?.fileUrl) {
                    try {
                      console.log('Downloading from dialog:', previewFile.name);
                      const response = await fetch(previewFile.fileUrl, {
                        method: 'GET',
                        headers: {
                          'Authorization': `Token ${localStorage.getItem('token')}`,
                        },
                        credentials: 'include'
                      });

                      if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                      }

                      const blob = await response.blob();
                      console.log('Dialog download blob size:', blob.size);

                      if (blob.size === 0) {
                        throw new Error('Received empty file');
                      }

                      const url = window.URL.createObjectURL(blob);
                      const link = document.createElement('a');
                      link.href = url;
                      link.download = previewFile.name;
                      document.body.appendChild(link);
                      link.click();

                      setTimeout(() => {
                        document.body.removeChild(link);
                        window.URL.revokeObjectURL(url);
                      }, 100);
                    } catch (error) {
                      console.error('Dialog download failed:', error);
                      alert(`Erreur de téléchargement: ${error.message}`);
                    }
                  }
                }}
                sx={{ textTransform: 'none' }}
              >
                Télécharger
              </Button>
              <Button onClick={handleClosePreview} sx={{ textTransform: 'none' }}>
                Fermer
              </Button>
            </DialogActions>
          )}
        </Dialog>

        {/* Success/Error Snackbar */}
        <Snackbar
          open={snackbarOpen}
          autoHideDuration={3000}
          onClose={handleSnackbarClose}
          message={snackbarMessage}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        />
      </Container>
    </Box>
  );
};

export default ReferenceDocumentsPage;
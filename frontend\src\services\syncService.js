import {
  getInspections,
  fetchUserFiles,
  uploadUserFiles,
  deleteUserFile
} from './api.js';
import {
  saveInspectionLocally,
  getLocalInspections,
  saveUserFileLocally,
  getLocalUserFiles,
  addToSyncQueue,
  getSyncQueue,
  removeFromSyncQueue,
  addPendingUpload,
  getPendingUploads,
  removePendingUpload
} from '../utils/indexedDB.js';
import { isOnline, waitForOnline } from '../utils/offlineUtils.js';

class SyncService {
  constructor() {
    this.isSyncing = false;
    this.syncInProgress = new Set();
  }

  // Sync inspections from server to local
  async syncInspections() {
    if (!isOnline() || this.isSyncing) return;

    this.isSyncing = true;
    try {
      console.log('Syncing inspections...');
      const serverInspections = await getInspections();

      // Save to local storage
      for (const inspection of serverInspections) {
        await saveInspectionLocally(inspection);
      }

      console.log('Inspections synced successfully');
    } catch (error) {
      console.error('Error syncing inspections:', error);
    } finally {
      this.isSyncing = false;
    }
  }

  // Sync user files from server to local
  async syncUserFiles(folderId = null) {
    if (!isOnline() || this.isSyncing) return;

    this.isSyncing = true;
    try {
      console.log('Syncing user files...');
      const serverFiles = await fetchUserFiles(folderId);

      // Save to local storage
      for (const file of serverFiles) {
        await saveUserFileLocally(file);
      }

      console.log('User files synced successfully');
    } catch (error) {
      console.error('Error syncing user files:', error);
    } finally {
      this.isSyncing = false;
    }
  }

  // Sync pending operations to server
  async syncPendingOperations() {
    if (!isOnline() || this.isSyncing) return;

    this.isSyncing = true;
    try {
      console.log('Syncing pending operations...');

      // Get pending uploads
      const pendingUploads = await getPendingUploads();

      for (const upload of pendingUploads) {
        try {
          if (upload.type === 'file') {
            await uploadUserFiles(upload.data.folderId, [upload.data.file]);
            await removePendingUpload(upload.id);
          }
        } catch (error) {
          console.error('Error syncing pending upload:', error);
          // Could implement retry logic here
        }
      }

      // Get sync queue
      const syncQueue = await getSyncQueue();

      for (const item of syncQueue) {
        try {
          if (item.operation === 'delete_file') {
            await deleteUserFile(item.data.fileId);
            await removeFromSyncQueue(item.id);
          }
          // Add other operations as needed
        } catch (error) {
          console.error('Error syncing queued operation:', error);
        }
      }

      console.log('Pending operations synced successfully');
    } catch (error) {
      console.error('Error syncing pending operations:', error);
    } finally {
      this.isSyncing = false;
    }
  }

  // Get data with fallback to local storage
  async getInspectionsWithFallback() {
    try {
      if (isOnline()) {
        const serverData = await getInspections();
        // Update local storage in background
        serverData.forEach(inspection => saveInspectionLocally(inspection));
        return serverData;
      } else {
        console.log('Offline: Loading inspections from local storage');

        // Get synced inspections from IndexedDB
        const localInspections = await getLocalInspections();

        // Get pending inspections from localStorage
        const pendingInspections = JSON.parse(localStorage.getItem('pendingInspections') || '[]');

        // Merge both sources, ensuring pending inspections have required fields
        const mergedInspections = [...localInspections];

        // Add pending inspections with proper structure
        pendingInspections.forEach((pending, index) => {
          // Create a temporary ID for pending inspections
          const tempId = `pending_${index}_${Date.now()}`;

          const completePendingInspection = {
            id: tempId,
            fiche_num: pending.fiche_num || `PENDING_${tempId.slice(-6)}`,
            date: pending.date || new Date().toISOString().split('T')[0],
            projet: pending.projet || '',
            equipement: pending.equipement || '',
            equipement_custom: pending.equipement_custom || '',
            tag: pending.tag || '',
            constructeur: pending.constructeur || '',
            model: pending.model || '',
            numero_serie: pending.numero_serie || '',
            date_installation: pending.date_installation || '',
            age: pending.age || '',
            puissance: pending.puissance || '',
            courant: pending.courant || '',
            tension: pending.tension || '',
            unite: pending.unite || '',
            unite_custom: pending.unite_custom || '',
            localisation: pending.localisation || '',
            zone_atex: pending.zone_atex || '',
            groupe_gaz: pending.groupe_gaz || '',
            classe_t: pending.classe_t || '',
            marquage_atex_g: pending.marquage_atex_g || '',
            marquage_atex_d: pending.marquage_atex_d || '',
            marquage_us: pending.marquage_us || '',
            type_marquage: pending.type_marquage || '',
            mode_protection: pending.mode_protection || '',
            organisme_notifie: pending.organisme_notifie || '',
            ip: pending.ip || '',
            nema: pending.nema || '',
            certificat: pending.certificat || '',
            tamb_min: pending.tamb_min || '',
            tamb_max: pending.tamb_max || '',
            atex_oui: pending.atex_oui || false,
            atex_non: pending.atex_non || false,
            acces_inaccessible: pending.acces_inaccessible || false,
            acces_calorifuge: pending.acces_calorifuge || false,
            acces_peinte: pending.acces_peinte || false,
            acces_inaccessible_plaque: pending.acces_inaccessible_plaque || false,
            acces_illisible: pending.acces_illisible || false,
            acces_pas_plaque: pending.acces_pas_plaque || false,
            niveau_1: pending.niveau_1 || false,
            niveau_2: pending.niveau_2 || false,
            niveau_3: pending.niveau_3 || false,
            points: pending.points || {},
            action: pending.action || '',
            date_precedente_inspection: pending.date_precedente_inspection || '',
            inspecteur: pending.inspecteur || '',
            qualifications: pending.qualifications || '',
            numero_certificat: pending.numero_certificat || '',
            observations_complementaires: pending.observations_complementaires || '',
            inspected_company_name: pending.inspected_company_name || '',
            // Mark as pending
            isPending: true,
            submittedAt: pending.submittedAt,
            status: pending.status
          };

          mergedInspections.push(completePendingInspection);
        });

        console.log(`Loaded ${localInspections.length} synced and ${pendingInspections.length} pending inspections`);
        return mergedInspections;
      }
    } catch (error) {
      console.error('Error getting inspections:', error);
      // Fallback to local storage
      return await getLocalInspections();
    }
  }

  // Get user files with fallback to local storage
  async getUserFilesWithFallback(folderId = null) {
    try {
      if (isOnline()) {
        const serverData = await fetchUserFiles(folderId);
        // Update local storage in background
        serverData.forEach(file => saveUserFileLocally(file));
        return serverData;
      } else {
        console.log('Offline: Loading user files from local storage');
        return await getLocalUserFiles(folderId);
      }
    } catch (error) {
      console.error('Error getting user files:', error);
      // Fallback to local storage
      return await getLocalUserFiles(folderId);
    }
  }

  // Queue file upload for later sync
  async queueFileUpload(folderId, file) {
    if (isOnline()) {
      try {
        return await uploadUserFiles(folderId, [file]);
      } catch (error) {
        console.error('Upload failed, queuing for later:', error);
        await addPendingUpload('file', { folderId, file });
        throw error; // Still throw to show error to user
      }
    } else {
      console.log('Offline: Queuing file upload');
      await addPendingUpload('file', { folderId, file });
      return { queued: true, message: 'File queued for upload when online' };
    }
  }

  // Queue file deletion for later sync
  async queueFileDeletion(fileId) {
    if (isOnline()) {
      try {
        return await deleteUserFile(fileId);
      } catch (error) {
        console.error('Delete failed, queuing for later:', error);
        await addToSyncQueue('delete_file', { fileId });
        throw error;
      }
    } else {
      console.log('Offline: Queuing file deletion');
      await addToSyncQueue('delete_file', { fileId });
      return { queued: true, message: 'File deletion queued when online' };
    }
  }

  // Sync pending inspections from local storage
  async syncPendingInspections() {
    if (!isOnline() || this.isSyncing) return;

    this.isSyncing = true;
    try {
      console.log('Syncing pending inspections...');

      const pendingInspections = JSON.parse(localStorage.getItem('pendingInspections') || '[]');

      if (pendingInspections.length === 0) {
        console.log('No pending inspections to sync');
        return;
      }

      const syncedInspections = [];
      const failedInspections = [];

      for (const inspection of pendingInspections) {
        try {
          // Submit the inspection to the server
          const formData = new FormData();

          // Add all form fields to FormData
          Object.keys(inspection).forEach(key => {
            if (key === 'photos_anomalies' && Array.isArray(inspection[key])) {
              inspection[key].forEach((file, index) => {
                formData.append(`photos_anomalies`, file);
              });
            } else if (key === 'photo_marquage' || key === 'photo_equipement') {
              if (inspection[key]) {
                formData.append(key, inspection[key]);
              }
            } else if (key !== 'submittedAt' && key !== 'status') {
              formData.append(key, inspection[key]);
            }
          });

          const response = await fetch('http://***************/api/inspections/inspections/', {
            method: 'POST',
            headers: {
              'Authorization': `Token ${localStorage.getItem('authToken')}`,
            },
            body: formData
          });

          if (response.ok) {
            syncedInspections.push(inspection);
            console.log('Successfully synced inspection:', inspection.fiche_num);
          } else {
            failedInspections.push(inspection);
            console.error('Failed to sync inspection:', inspection.fiche_num, response.status);
          }
        } catch (error) {
          failedInspections.push(inspection);
          console.error('Error syncing inspection:', error);
        }
      }

      // Update local storage - remove synced inspections
      localStorage.setItem('pendingInspections', JSON.stringify(failedInspections));

      console.log(`Synced ${syncedInspections.length} inspections, ${failedInspections.length} failed`);

      if (syncedInspections.length > 0) {
        alert(`${syncedInspections.length} inspection(s) synchronisée(s) avec succès!`);
      }

    } catch (error) {
      console.error('Error syncing pending inspections:', error);
    } finally {
      this.isSyncing = false;
    }
  }

  // Start periodic sync
  startPeriodicSync(intervalMinutes = 5) {
    const intervalMs = intervalMinutes * 60 * 1000;
    setInterval(async () => {
      if (isOnline() && !this.isSyncing) {
        await this.syncInspections();
        await this.syncUserFiles();
        await this.syncPendingOperations();
        await this.syncPendingInspections();
      }
    }, intervalMs);
  }

  // Manual sync trigger
  async manualSync() {
    if (!isOnline()) {
      throw new Error('Cannot sync while offline');
    }

    await this.syncInspections();
    await this.syncUserFiles();
    await this.syncPendingOperations();
    await this.syncPendingInspections();
  }

  // Get count of pending inspections
  getPendingInspectionsCount() {
    try {
      const pendingInspections = JSON.parse(localStorage.getItem('pendingInspections') || '[]');
      return pendingInspections.length;
    } catch (error) {
      console.error('Error getting pending inspections count:', error);
      return 0;
    }
  }
}

// Create singleton instance
export const syncService = new SyncService();

// Export convenience functions
export const getInspectionsWithSync = syncService.getInspectionsWithFallback.bind(syncService);
export const getUserFilesWithSync = syncService.getUserFilesWithFallback.bind(syncService);
export const queueFileUpload = syncService.queueFileUpload.bind(syncService);
export const queueFileDeletion = syncService.queueFileDeletion.bind(syncService);
export const manualSync = syncService.manualSync.bind(syncService);
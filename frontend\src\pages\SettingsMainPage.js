import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Avatar,
  Alert,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Divider,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  IconButton
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Business as BusinessIcon,
  Engineering as EngineeringIcon,
  Image as ImageIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Visibility as VisibilityIcon
} from '@mui/icons-material';
import api from '../services/api';

const SettingsMainPage = () => {
  const [companies, setCompanies] = useState([]);
  const [logos, setLogos] = useState([]);
  const [selectedCompany, setSelectedCompany] = useState('');
  const [newCompanyName, setNewCompanyName] = useState('');
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [deleteDialog, setDeleteDialog] = useState({ open: false, logoId: null });
  const [addCompanyDialog, setAddCompanyDialog] = useState(false);
  const [logoPreview, setLogoPreview] = useState({ performing_company: null, inspected_company: null });

   const apiBase = (process.env.REACT_APP_API_URL) || 'http://197.140.142.170';

  useEffect(() => {
    fetchCompanies();
    fetchLogos();
  }, []);

  // Update logo preview when selected company changes
  useEffect(() => {
    if (selectedCompany) {
      // Clear any file-based previews when switching companies
      setLogoPreview({ performing_company: null, inspected_company: null });
    }
  }, [selectedCompany]);

  const fetchCompanies = async () => {
    try {
      const response = await api.get('/api/inspections/companies/');
      setCompanies(response.data);
    } catch (error) {
      console.error('Error fetching companies:', error);
      showSnackbar('Erreur lors du chargement des entreprises', 'error');
    }
  };

  const fetchLogos = async () => {
    try {
      const response = await api.get('/api/inspections/settings/logos/');
      const logos = response.data.logos || [];

      // Ensure logo URLs are absolute and properly formatted
      const processedLogos = logos.map(logo => {
        let logoUrl = logo.logo_url;
        if (logoUrl) {
          // If it's already absolute, use as is
          if (logoUrl.startsWith('http')) {
            logoUrl = logoUrl;
          }
          // If it starts with /media/, prepend the backend URL
          else if (logoUrl.startsWith('/media/')) {
            logoUrl = `${apiBase}${logoUrl}`;
          }
          // If it's just the filename or relative path, construct full URL
          else {
            logoUrl = `${apiBase}/media/${logoUrl}`;
          }
        }

        return {
          ...logo,
          logo_url: logoUrl
        };
      });

      setLogos(processedLogos);
      console.log('Fetched logos:', processedLogos);
    } catch (error) {
      console.error('Error fetching logos:', error);
      showSnackbar('Erreur lors du chargement des logos', 'error');
    }
  };

  const addCompany = async () => {
    if (!newCompanyName.trim()) {
      showSnackbar('Nom de l\'entreprise requis', 'error');
      return;
    }

    try {
      setLoading(true);
      const response = await api.post('/api/inspections/companies/', {
        name: newCompanyName.trim()
      });

      setCompanies(prev => [...prev, response.data]);
      setNewCompanyName('');
      setAddCompanyDialog(false);
      showSnackbar('Entreprise ajoutée avec succès', 'success');
    } catch (error) {
      console.error('Error adding company:', error);
      showSnackbar('Erreur lors de l\'ajout de l\'entreprise', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = async (event, logoType) => {
    const file = event.target.files[0];
    if (!file) return;

    if (!selectedCompany) {
      showSnackbar('Veuillez sélectionner une entreprise', 'error');
      return;
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
      showSnackbar('Format de fichier non supporté. Utilisez JPG, PNG ou GIF.', 'error');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      showSnackbar('Le fichier est trop volumineux. Taille maximale: 5MB.', 'error');
      return;
    }

    // Show preview immediately
    const reader = new FileReader();
    reader.onload = (e) => {
      setLogoPreview(prev => ({
        ...prev,
        [logoType]: e.target.result
      }));
    };
    reader.readAsDataURL(file);

    try {
      setLoading(true);
      const formData = new FormData();
      formData.append('logo', file);
      formData.append('logo_type', logoType);
      formData.append('company_name', selectedCompany);

      const response = await api.post('/api/inspections/settings/logos/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      // Refresh logos
      await fetchLogos();

      // Clear the preview since the logo is now saved in database
      setLogoPreview(prev => ({
        ...prev,
        [logoType]: null
      }));

      const companyName = logoType === 'performing_company'
        ? 'de l\'entreprise réalisatrice'
        : 'de l\'entreprise inspectée';
      showSnackbar(`Logo ${companyName} ajouté avec succès pour ${selectedCompany}`, 'success');
    } catch (error) {
      console.error('Error uploading logo:', error);
      showSnackbar('Erreur lors du téléchargement du logo', 'error');
      // Reset preview on error
      setLogoPreview(prev => ({
        ...prev,
        [logoType]: null
      }));
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteLogo = async (logoId) => {
    try {
      setLoading(true);
      await api.delete('/api/inspections/settings/logos/', {
        data: { logo_id: logoId }
      });

      // Refresh logos
      await fetchLogos();
      showSnackbar('Logo supprimé avec succès', 'success');
      setDeleteDialog({ open: false, logoId: null });
    } catch (error) {
      console.error('Error deleting logo:', error);
      showSnackbar('Erreur lors de la suppression du logo', 'error');
    } finally {
      setLoading(false);
    }
  };

  const getCompanyLogos = (companyName) => {
    return logos.filter(logo => logo.company_name === companyName);
  };

  const getLogoForCompany = (companyName, logoType) => {
    const logo = logos.find(logo => logo.company_name === companyName && logo.logo_type === logoType);
    console.log(`Looking for logo: company=${companyName}, type=${logoType}, found:`, logo);
    return logo;
  };

  const showSnackbar = (message, severity) => {
    setSnackbar({ open: true, message, severity });
  };

  const LogoCard = ({ logoType, title, icon, description }) => {
    const existingLogo = selectedCompany ? getLogoForCompany(selectedCompany, logoType) : null;
    const previewImage = logoPreview[logoType];
    // Show preview if available, otherwise show existing logo from database
    const displayImage = previewImage || (existingLogo ? existingLogo.logo_url : null);
    const isPreview = !!previewImage;

    console.log(`LogoCard ${logoType}:`, {
      selectedCompany,
      existingLogo,
      previewImage: !!previewImage,
      displayImage,
      isPreview
    });

    return (
      <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        <CardContent sx={{ flexGrow: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            {icon}
            <Typography variant="h6" sx={{ ml: 1 }}>
              {title}
            </Typography>
          </Box>

          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {description}
          </Typography>

          <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
            {displayImage ? (
              <Box sx={{ position: 'relative' }}>
                <Avatar
                  src={displayImage}
                  sx={{
                    width: 120,
                    height: 120,
                    border: '2px solid #e0e0e0',
                    borderRadius: 2
                  }}
                  variant="rounded"
                />
                {isPreview && (
                  <Chip
                    label="Aperçu"
                    size="small"
                    color="primary"
                    sx={{
                      position: 'absolute',
                      top: -8,
                      right: -8,
                      fontSize: '0.7rem'
                    }}
                  />
                )}
              </Box>
            ) : (
              <Box
                sx={{
                  width: 120,
                  height: 120,
                  border: '2px dashed #ccc',
                  borderRadius: 2,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: '#f5f5f5'
                }}
              >
                <ImageIcon sx={{ fontSize: 40, color: '#ccc' }} />
              </Box>
            )}
          </Box>
        </CardContent>

        <CardActions sx={{ justifyContent: 'center', p: 2 }}>
          <input
            accept="image/*"
            style={{ display: 'none' }}
            id={`upload-${logoType}`}
            type="file"
            onChange={(e) => handleFileUpload(e, logoType)}
            disabled={!selectedCompany}
          />
          <label htmlFor={`upload-${logoType}`}>
            <Button
              variant="contained"
              component="span"
              startIcon={<UploadIcon />}
              disabled={loading || !selectedCompany}
              sx={{ mr: 1 }}
            >
              {existingLogo ? 'Remplacer' : 'Ajouter'}
            </Button>
          </label>

          {existingLogo && (
            <Button
              variant="outlined"
              color="error"
              startIcon={<DeleteIcon />}
              onClick={() => setDeleteDialog({ open: true, logoId: existingLogo.id })}
              disabled={loading}
            >
              Supprimer
            </Button>
          )}

          {existingLogo && (
            <IconButton
              color="primary"
              onClick={() => window.open(existingLogo.logo_url, '_blank')}
              disabled={loading}
              sx={{ ml: 1 }}
            >
              <VisibilityIcon />
            </IconButton>
          )}
        </CardActions>
      </Card>
    );
  };

  return (
<Box
  sx={{
    backgroundColor: '#ffffff',
    ml: '280px',
    pt: 0,
    pb: 4,
    px: 3,
    width: `calc(100% - 280px)`,
    boxSizing: 'border-box',
    marginTop: '-600px'
  }}
>
      <Typography variant="h4" sx={{ color: '#1976d2', mb: 3, mt: 0 }}>
        Paramètres Principaux
      </Typography>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
          <BusinessIcon sx={{ mr: 1 }} />
          Configuration des Logos d'Entreprise
        </Typography>

        <Divider sx={{ my: 2 }} />

        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          Configurez les logos spécifiques à chaque entreprise qui apparaîtront dans les rapports d'inspection PDF.
          Sélectionnez une entreprise pour gérer ses logos.
        </Typography>

        {/* Company Selection Section */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Gestion des Entreprises
          </Typography>

          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={8}>
             <FormControl fullWidth>
  <InputLabel>Sélectionner une entreprise</InputLabel>
  <Select
    value={selectedCompany}
    onChange={(e) => {
      setSelectedCompany(e.target.value);
      setLogoPreview({ performing_company: null, inspected_company: null });
    }}
    label="Sélectionner une entreprise"
    displayEmpty
    // 👇 Key: Ensure consistent visual width
    sx={{
      minWidth: 300, // Minimum width for the select input
      '& .MuiSelect-select': {
        minWidth: 300,
      },
    }}
    MenuProps={{
      PaperProps: {
        style: {
          minWidth: 300, // Same as input
          maxWidth: 'none', // Prevent clipping of wide items
        },
      },
      // Prevent the menu from anchoring based on content width
      anchorOrigin: {
        vertical: 'bottom',
        horizontal: 'left',
      },
      transformOrigin: {
        vertical: 'top',
        horizontal: 'left',
      },
    }}
  >
    {companies.length === 0 ? (
      <MenuItem disabled>Pas d'entreprises disponibles</MenuItem>
    ) : (
      companies.map((company) => (
        <MenuItem key={company.id} value={company.name}>
          {company.name}
        </MenuItem>
      ))
    )}
  </Select>
</FormControl>
            </Grid>

            <Grid item xs={12} md={3}>
              <Button
                variant="outlined"
                startIcon={<AddIcon />}
                onClick={() => setAddCompanyDialog(true)}
                disabled={loading}
              >
                Ajouter une entreprise
              </Button>
            </Grid>

            <Grid item xs={12} md={3}>
        
            </Grid>
          </Grid>

          {selectedCompany && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" color="primary">
                Entreprise sélectionnée: <strong>{selectedCompany}</strong>
              </Typography>

              {/* Debug info */}
              <Box sx={{ mt: 1, p: 1, bgcolor: '#f5f5f5', borderRadius: 1 }}>
                <Typography variant="caption" color="text.secondary">
                  Debug: Logos pour {selectedCompany}: {getCompanyLogos(selectedCompany).length} trouvé(s)
                </Typography>
                {getCompanyLogos(selectedCompany).map(logo => (
                  <Typography key={logo.id} variant="caption" display="block" color="text.secondary">
                    - {logo.logo_type}: {logo.logo_url ? 'URL disponible' : 'Pas d\'URL'}
                  </Typography>
                ))}
              </Box>
            </Box>
          )}
        </Box>

        <Divider sx={{ my: 3 }} />

        {/* Logo Configuration Section */}
        <Typography variant="h6" sx={{ mb: 2 }}>
          Configuration des Logos
        </Typography>

        {!selectedCompany && (
          <Alert severity="info" sx={{ mb: 3 }}>
            Veuillez sélectionner une entreprise pour configurer ses logos.
          </Alert>
        )}

        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
            <CircularProgress />
          </Box>
        )}

        <Grid container spacing={3} sx={{ opacity: selectedCompany ? 1 : 0.5 }}>
          <Grid item xs={12} md={6}>
            <LogoCard
              logoType="performing_company"
              title="Entreprise Réalisatrice"
              icon={<EngineeringIcon color="primary" />}
              description="Logo de l'entreprise qui effectue l'inspection. Ce logo apparaîtra dans la première colonne de l'en-tête du rapport."
            />
          </Grid>
          
          <Grid item xs={12} md={6}>
            <LogoCard
              logoType="inspected_company"
              title="Entreprise Inspectée"
              icon={<BusinessIcon color="secondary" />}
              description="Logo de l'entreprise dont les équipements sont inspectés. Ce logo apparaîtra dans la deuxième colonne de l'en-tête du rapport."
            />
          </Grid>
        </Grid>

        <Alert severity="info" sx={{ mt: 3 }}>
          <Typography variant="body2">
            <strong>Formats supportés:</strong> JPG, PNG, GIF<br />
            <strong>Taille maximale:</strong> 5MB<br />
            <strong>Recommandation:</strong> Utilisez des images carrées ou rectangulaires pour un meilleur rendu
          </Typography>
        </Alert>
      </Paper>

      {/* Add Company Dialog */}
      <Dialog
        open={addCompanyDialog}
        onClose={() => setAddCompanyDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Ajouter une nouvelle entreprise</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Nom de l'entreprise"
            fullWidth
            variant="outlined"
            value={newCompanyName}
            onChange={(e) => setNewCompanyName(e.target.value)}
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddCompanyDialog(false)}>
            Annuler
          </Button>
          <Button
            onClick={addCompany}
            variant="contained"
            disabled={loading || !newCompanyName.trim()}
          >
            Ajouter
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Logo Confirmation Dialog */}
      <Dialog
        open={deleteDialog.open}
        onClose={() => setDeleteDialog({ open: false, logoId: null })}
      >
        <DialogTitle>Confirmer la suppression</DialogTitle>
        <DialogContent>
          <Typography>
            Êtes-vous sûr de vouloir supprimer ce logo ? Cette action est irréversible.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog({ open: false, logoId: null })}>
            Annuler
          </Button>
          <Button
            onClick={() => handleDeleteLogo(deleteDialog.logoId)}
            color="error"
            disabled={loading}
          >
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert 
          onClose={() => setSnackbar({ ...snackbar, open: false })} 
          severity={snackbar.severity}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default SettingsMainPage;
"filename", "language", "JavaScript JSX", "PostCSS", "XML", "JSON", "Markdown", "HTML", "Python", "pip requirements", "Shell Script", "comment", "blank", "total"
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\authentication\__init__.py", "Python", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\authentication\admin.py", "Python", 0, 0, 0, 0, 0, 0, -1, 0, 0, -1, -2, -4
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\authentication\apps.py", "Python", 0, 0, 0, 0, 0, 0, -4, 0, 0, 0, -3, -7
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\authentication\migrations\0001_initial.py", "Python", 0, 0, 0, 0, 0, 0, -21, 0, 0, -1, -7, -29
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\authentication\migrations\__init__.py", "Python", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\authentication\models.py", "Python", 0, 0, 0, 0, 0, 0, -14, 0, 0, -1, -4, -19
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\authentication\serializers.py", "Python", 0, 0, 0, 0, 0, 0, -27, 0, 0, 0, -5, -32
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\authentication\tests.py", "Python", 0, 0, 0, 0, 0, 0, -1, 0, 0, -1, -2, -4
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\authentication\urls.py", "Python", 0, 0, 0, 0, 0, 0, -12, 0, 0, 0, -1, -13
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\authentication\views.py", "Python", 0, 0, 0, 0, 0, 0, -125, 0, 0, -14, -37, -176
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\build.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, -18, -6, -7, -31
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\config\__init__.py", "Python", 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, -1
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\config\asgi.py", "Python", 0, 0, 0, 0, 0, 0, -4, 0, 0, -8, -4, -16
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\config\settings.py", "Python", 0, 0, 0, 0, 0, 0, -144, 0, 0, -13, -18, -175
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\config\urls.py", "Python", 0, 0, 0, 0, 0, 0, -11, 0, 0, 0, -1, -12
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\config\wsgi.py", "Python", 0, 0, 0, 0, 0, 0, -4, 0, 0, -8, -3, -15
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\einspection\__init__.py", "Python", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\einspection\asgi.py", "Python", 0, 0, 0, 0, 0, 0, -4, 0, 0, -8, -5, -17
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\einspection\settings.py", "Python", 0, 0, 0, 0, 0, 0, -119, 0, 0, -33, -36, -188
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\einspection\urls.py", "Python", 0, 0, 0, 0, 0, 0, -10, 0, 0, -16, -3, -29
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\einspection\wsgi.py", "Python", 0, 0, 0, 0, 0, 0, -4, 0, 0, -8, -5, -17
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\migrations\0001_initial.py", "Python", 0, 0, 0, 0, 0, 0, -6, 0, 0, -1, -6, -13
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\migrations\0002_initial.py", "Python", 0, 0, 0, 0, 0, 0, -62, 0, 0, -1, -7, -70
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\migrations\0003_auto_20250611_1602.py", "Python", 0, 0, 0, 0, 0, 0, -7, 0, 0, -1, -6, -14
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\migrations\0004_remove_inspection_marquage_us_cl_and_more.py", "Python", 0, 0, 0, 0, 0, 0, -71, 0, 0, -1, -6, -78
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\migrations\0005_inspection_photo_equipement_and_more.py", "Python", 0, 0, 0, 0, 0, 0, -22, 0, 0, -1, -6, -29
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\migrations\0006_inspection_fiche_generation_method.py", "Python", 0, 0, 0, 0, 0, 0, -12, 0, 0, -1, -6, -19
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\migrations\0007_remove_inspection_fiche_generation_method_and_more.py", "Python", 0, 0, 0, 0, 0, 0, -131, 0, 0, -1, -6, -138
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\migrations\0008_inspection_fiche_generation_method.py", "Python", 0, 0, 0, 0, 0, 0, -12, 0, 0, -1, -6, -19
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\migrations\__init__.py", "Python", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, -1
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\models.py", "Python", 0, 0, 0, 0, 0, 0, -58, 0, 0, -1, -5, -64
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\serializers.py", "Python", 0, 0, 0, 0, 0, 0, -52, 0, 0, -2, -9, -63
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\urls.py", "Python", 0, 0, 0, 0, 0, 0, -9, 0, 0, 0, -4, -13
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\inspection\views.py", "Python", 0, 0, 0, 0, 0, 0, -1360, 0, 0, -161, -178, -1699
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\manage.py", "Python", 0, 0, 0, 0, 0, 0, -15, 0, 0, -3, -5, -23
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\package-lock.json", "JSON", 0, 0, 0, -268, 0, 0, 0, 0, 0, 0, -1, -269
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\package.json", "JSON", 0, 0, 0, -5, 0, 0, 0, 0, 0, 0, -1, -6
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\requirements.txt", "pip requirements", 0, 0, 0, 0, 0, 0, 0, -10, 0, 0, 0, -10
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\backend\test_db.py", "Python", 0, 0, 0, 0, 0, 0, -22, 0, 0, -2, -3, -27
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\README.md", "Markdown", 0, 0, 0, 0, 38, 0, 0, 0, 0, 0, 33, 71
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\package-lock.json", "JSON", 0, 0, 0, 16850, 0, 0, 0, 0, 0, 0, 1, 16851
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\package.json", "JSON", 0, 0, 0, 47, 0, 0, 0, 0, 0, 0, 1, 48
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\public\index.html", "HTML", 0, 0, 0, 0, 0, 20, 0, 0, 0, 23, 1, 44
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\public\manifest.json", "JSON", 0, 0, 0, 25, 0, 0, 0, 0, 0, 0, 1, 26
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\App.css", "PostCSS", 0, 33, 0, 0, 0, 0, 0, 0, 0, 0, 6, 39
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\App.js", "JavaScript JSX", 37, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4, 42
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\App.test.js", "JavaScript JSX", 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 9
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\components\PrivateRoute.js", "JavaScript JSX", 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 13
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\index.css", "PostCSS", 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 2, 14
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\index.js", "JavaScript JSX", 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 15
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\logo.svg", "XML", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\pages\DashboardPage.js", "JavaScript JSX", 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 22
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\pages\Documents.js", "JavaScript JSX", 479, 0, 0, 0, 0, 0, 0, 0, 0, 23, 41, 543
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\pages\InspectionFormPage.js", "JavaScript JSX", 1538, 0, 0, 0, 0, 0, 0, 0, 0, 95, 161, 1794
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\pages\LoginPage.js", "JavaScript JSX", 313, 0, 0, 0, 0, 0, 0, 0, 0, 6, 15, 334
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\pages\global\Sidebar.jsx", "JavaScript JSX", 227, 0, 0, 0, 0, 0, 0, 0, 0, 10, 19, 256
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\pages\global\Topbar.jsx", "JavaScript JSX", 37, 0, 0, 0, 0, 0, 0, 0, 0, 2, 5, 44
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\reportWebVitals.js", "JavaScript JSX", 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 14
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\services\api.js", "JavaScript JSX", 103, 0, 0, 0, 0, 0, 0, 0, 0, 7, 13, 123
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\services\mockApi.js", "JavaScript JSX", 22, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 24
"c:\Users\<USER>\Documents\ilhem\APSEC\e-inspection\frontend\src\setupTests.js", "JavaScript JSX", 1, 0, 0, 0, 0, 0, 0, 0, 0, 4, 1, 6
"Total", "-", 2818, 45, 1, 16649, 38, 20, -2344, -10, -18, -124, -85, 16990
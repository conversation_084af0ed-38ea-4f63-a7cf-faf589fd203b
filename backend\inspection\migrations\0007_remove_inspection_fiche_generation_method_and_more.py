# Generated by Django 5.1 on 2025-07-03 15:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inspection', '0006_inspection_fiche_generation_method'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='inspection',
            name='fiche_generation_method',
        ),
        migrations.AlterField(
            model_name='inspection',
            name='certificat',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='inspection',
            name='classe_t',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='inspection',
            name='constructeur',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='inspection',
            name='courant',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='inspection',
            name='date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='inspection',
            name='equipement',
            field=models.Char<PERSON>ield(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='inspection',
            name='groupe_gaz',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='inspection',
            name='ip',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='inspection',
            name='localisation',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='inspection',
            name='marquage_atex_d',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='inspection',
            name='marquage_atex_g',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='inspection',
            name='mode_protection',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='inspection',
            name='model',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='inspection',
            name='nema',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='inspection',
            name='organisme_notifie',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='inspection',
            name='projet',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='inspection',
            name='puissance',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='inspection',
            name='tag',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='inspection',
            name='tamb_max',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='inspection',
            name='tamb_min',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='inspection',
            name='tension',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='inspection',
            name='type_marquage',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='inspection',
            name='unite',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='inspection',
            name='zone_atex',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
    ]

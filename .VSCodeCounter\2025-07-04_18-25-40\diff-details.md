# Diff Details

Date : 2025-07-04 18:25:40

Directory c:\\Users\\<USER>\\Documents\\ilhem\\APSEC\\e-inspection\\frontend

Total : 61 files,  17199 codes, -124 comments, -85 blanks, all 16990 lines

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [backend/authentication/\_\_init\_\_.py](/backend/authentication/__init__.py) | Python | 0 | 0 | -1 | -1 |
| [backend/authentication/admin.py](/backend/authentication/admin.py) | Python | -1 | -1 | -2 | -4 |
| [backend/authentication/apps.py](/backend/authentication/apps.py) | Python | -4 | 0 | -3 | -7 |
| [backend/authentication/migrations/0001\_initial.py](/backend/authentication/migrations/0001_initial.py) | Python | -21 | -1 | -7 | -29 |
| [backend/authentication/migrations/\_\_init\_\_.py](/backend/authentication/migrations/__init__.py) | Python | 0 | 0 | -1 | -1 |
| [backend/authentication/models.py](/backend/authentication/models.py) | Python | -14 | -1 | -4 | -19 |
| [backend/authentication/serializers.py](/backend/authentication/serializers.py) | Python | -27 | 0 | -5 | -32 |
| [backend/authentication/tests.py](/backend/authentication/tests.py) | Python | -1 | -1 | -2 | -4 |
| [backend/authentication/urls.py](/backend/authentication/urls.py) | Python | -12 | 0 | -1 | -13 |
| [backend/authentication/views.py](/backend/authentication/views.py) | Python | -125 | -14 | -37 | -176 |
| [backend/build.sh](/backend/build.sh) | Shell Script | -18 | -6 | -7 | -31 |
| [backend/config/\_\_init\_\_.py](/backend/config/__init__.py) | Python | 0 | -1 | 0 | -1 |
| [backend/config/asgi.py](/backend/config/asgi.py) | Python | -4 | -8 | -4 | -16 |
| [backend/config/settings.py](/backend/config/settings.py) | Python | -144 | -13 | -18 | -175 |
| [backend/config/urls.py](/backend/config/urls.py) | Python | -11 | 0 | -1 | -12 |
| [backend/config/wsgi.py](/backend/config/wsgi.py) | Python | -4 | -8 | -3 | -15 |
| [backend/einspection/\_\_init\_\_.py](/backend/einspection/__init__.py) | Python | 0 | 0 | -1 | -1 |
| [backend/einspection/asgi.py](/backend/einspection/asgi.py) | Python | -4 | -8 | -5 | -17 |
| [backend/einspection/settings.py](/backend/einspection/settings.py) | Python | -119 | -33 | -36 | -188 |
| [backend/einspection/urls.py](/backend/einspection/urls.py) | Python | -10 | -16 | -3 | -29 |
| [backend/einspection/wsgi.py](/backend/einspection/wsgi.py) | Python | -4 | -8 | -5 | -17 |
| [backend/inspection/migrations/0001\_initial.py](/backend/inspection/migrations/0001_initial.py) | Python | -6 | -1 | -6 | -13 |
| [backend/inspection/migrations/0002\_initial.py](/backend/inspection/migrations/0002_initial.py) | Python | -62 | -1 | -7 | -70 |
| [backend/inspection/migrations/0003\_auto\_20250611\_1602.py](/backend/inspection/migrations/0003_auto_20250611_1602.py) | Python | -7 | -1 | -6 | -14 |
| [backend/inspection/migrations/0004\_remove\_inspection\_marquage\_us\_cl\_and\_more.py](/backend/inspection/migrations/0004_remove_inspection_marquage_us_cl_and_more.py) | Python | -71 | -1 | -6 | -78 |
| [backend/inspection/migrations/0005\_inspection\_photo\_equipement\_and\_more.py](/backend/inspection/migrations/0005_inspection_photo_equipement_and_more.py) | Python | -22 | -1 | -6 | -29 |
| [backend/inspection/migrations/0006\_inspection\_fiche\_generation\_method.py](/backend/inspection/migrations/0006_inspection_fiche_generation_method.py) | Python | -12 | -1 | -6 | -19 |
| [backend/inspection/migrations/0007\_remove\_inspection\_fiche\_generation\_method\_and\_more.py](/backend/inspection/migrations/0007_remove_inspection_fiche_generation_method_and_more.py) | Python | -131 | -1 | -6 | -138 |
| [backend/inspection/migrations/0008\_inspection\_fiche\_generation\_method.py](/backend/inspection/migrations/0008_inspection_fiche_generation_method.py) | Python | -12 | -1 | -6 | -19 |
| [backend/inspection/migrations/\_\_init\_\_.py](/backend/inspection/migrations/__init__.py) | Python | 0 | 0 | -1 | -1 |
| [backend/inspection/models.py](/backend/inspection/models.py) | Python | -58 | -1 | -5 | -64 |
| [backend/inspection/serializers.py](/backend/inspection/serializers.py) | Python | -52 | -2 | -9 | -63 |
| [backend/inspection/urls.py](/backend/inspection/urls.py) | Python | -9 | 0 | -4 | -13 |
| [backend/inspection/views.py](/backend/inspection/views.py) | Python | -1,360 | -161 | -178 | -1,699 |
| [backend/manage.py](/backend/manage.py) | Python | -15 | -3 | -5 | -23 |
| [backend/package-lock.json](/backend/package-lock.json) | JSON | -268 | 0 | -1 | -269 |
| [backend/package.json](/backend/package.json) | JSON | -5 | 0 | -1 | -6 |
| [backend/requirements.txt](/backend/requirements.txt) | pip requirements | -10 | 0 | 0 | -10 |
| [backend/test\_db.py](/backend/test_db.py) | Python | -22 | -2 | -3 | -27 |
| [frontend/README.md](/frontend/README.md) | Markdown | 38 | 0 | 33 | 71 |
| [frontend/package-lock.json](/frontend/package-lock.json) | JSON | 16,850 | 0 | 1 | 16,851 |
| [frontend/package.json](/frontend/package.json) | JSON | 47 | 0 | 1 | 48 |
| [frontend/public/index.html](/frontend/public/index.html) | HTML | 20 | 23 | 1 | 44 |
| [frontend/public/manifest.json](/frontend/public/manifest.json) | JSON | 25 | 0 | 1 | 26 |
| [frontend/src/App.css](/frontend/src/App.css) | PostCSS | 33 | 0 | 6 | 39 |
| [frontend/src/App.js](/frontend/src/App.js) | JavaScript JSX | 37 | 1 | 4 | 42 |
| [frontend/src/App.test.js](/frontend/src/App.test.js) | JavaScript JSX | 7 | 0 | 2 | 9 |
| [frontend/src/components/PrivateRoute.js](/frontend/src/components/PrivateRoute.js) | JavaScript JSX | 9 | 0 | 4 | 13 |
| [frontend/src/index.css](/frontend/src/index.css) | PostCSS | 12 | 0 | 2 | 14 |
| [frontend/src/index.js](/frontend/src/index.js) | JavaScript JSX | 13 | 0 | 2 | 15 |
| [frontend/src/logo.svg](/frontend/src/logo.svg) | XML | 1 | 0 | 0 | 1 |
| [frontend/src/pages/DashboardPage.js](/frontend/src/pages/DashboardPage.js) | JavaScript JSX | 20 | 0 | 2 | 22 |
| [frontend/src/pages/Documents.js](/frontend/src/pages/Documents.js) | JavaScript JSX | 479 | 23 | 41 | 543 |
| [frontend/src/pages/InspectionFormPage.js](/frontend/src/pages/InspectionFormPage.js) | JavaScript JSX | 1,538 | 95 | 161 | 1,794 |
| [frontend/src/pages/LoginPage.js](/frontend/src/pages/LoginPage.js) | JavaScript JSX | 313 | 6 | 15 | 334 |
| [frontend/src/pages/global/Sidebar.jsx](/frontend/src/pages/global/Sidebar.jsx) | JavaScript JSX | 227 | 10 | 19 | 256 |
| [frontend/src/pages/global/Topbar.jsx](/frontend/src/pages/global/Topbar.jsx) | JavaScript JSX | 37 | 2 | 5 | 44 |
| [frontend/src/reportWebVitals.js](/frontend/src/reportWebVitals.js) | JavaScript JSX | 12 | 0 | 2 | 14 |
| [frontend/src/services/api.js](/frontend/src/services/api.js) | JavaScript JSX | 103 | 7 | 13 | 123 |
| [frontend/src/services/mockApi.js](/frontend/src/services/mockApi.js) | JavaScript JSX | 22 | 1 | 1 | 24 |
| [frontend/src/setupTests.js](/frontend/src/setupTests.js) | JavaScript JSX | 1 | 4 | 1 | 6 |

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details
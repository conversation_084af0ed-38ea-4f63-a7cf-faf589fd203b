# Details

Date : 2025-07-04 18:25:40

Directory c:\\Users\\<USER>\\Documents\\ilhem\\APSEC\\e-inspection\\frontend

Total : 22 files,  19844 codes, 172 comments, 317 blanks, all 20333 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [frontend/README.md](/frontend/README.md) | Markdown | 38 | 0 | 33 | 71 |
| [frontend/package-lock.json](/frontend/package-lock.json) | JSON | 16,850 | 0 | 1 | 16,851 |
| [frontend/package.json](/frontend/package.json) | JSON | 47 | 0 | 1 | 48 |
| [frontend/public/index.html](/frontend/public/index.html) | HTML | 20 | 23 | 1 | 44 |
| [frontend/public/manifest.json](/frontend/public/manifest.json) | JSON | 25 | 0 | 1 | 26 |
| [frontend/src/App.css](/frontend/src/App.css) | PostCSS | 33 | 0 | 6 | 39 |
| [frontend/src/App.js](/frontend/src/App.js) | JavaScript JSX | 37 | 1 | 4 | 42 |
| [frontend/src/App.test.js](/frontend/src/App.test.js) | JavaScript JSX | 7 | 0 | 2 | 9 |
| [frontend/src/components/PrivateRoute.js](/frontend/src/components/PrivateRoute.js) | JavaScript JSX | 9 | 0 | 4 | 13 |
| [frontend/src/index.css](/frontend/src/index.css) | PostCSS | 12 | 0 | 2 | 14 |
| [frontend/src/index.js](/frontend/src/index.js) | JavaScript JSX | 13 | 0 | 2 | 15 |
| [frontend/src/logo.svg](/frontend/src/logo.svg) | XML | 1 | 0 | 0 | 1 |
| [frontend/src/pages/DashboardPage.js](/frontend/src/pages/DashboardPage.js) | JavaScript JSX | 20 | 0 | 2 | 22 |
| [frontend/src/pages/Documents.js](/frontend/src/pages/Documents.js) | JavaScript JSX | 479 | 23 | 41 | 543 |
| [frontend/src/pages/InspectionFormPage.js](/frontend/src/pages/InspectionFormPage.js) | JavaScript JSX | 1,538 | 95 | 161 | 1,794 |
| [frontend/src/pages/LoginPage.js](/frontend/src/pages/LoginPage.js) | JavaScript JSX | 313 | 6 | 15 | 334 |
| [frontend/src/pages/global/Sidebar.jsx](/frontend/src/pages/global/Sidebar.jsx) | JavaScript JSX | 227 | 10 | 19 | 256 |
| [frontend/src/pages/global/Topbar.jsx](/frontend/src/pages/global/Topbar.jsx) | JavaScript JSX | 37 | 2 | 5 | 44 |
| [frontend/src/reportWebVitals.js](/frontend/src/reportWebVitals.js) | JavaScript JSX | 12 | 0 | 2 | 14 |
| [frontend/src/services/api.js](/frontend/src/services/api.js) | JavaScript JSX | 103 | 7 | 13 | 123 |
| [frontend/src/services/mockApi.js](/frontend/src/services/mockApi.js) | JavaScript JSX | 22 | 1 | 1 | 24 |
| [frontend/src/setupTests.js](/frontend/src/setupTests.js) | JavaScript JSX | 1 | 4 | 1 | 6 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)
// Sync Worker for handling background synchronization
// This runs in a separate context and handles IndexedDB operations

const DB_NAME = 'EInspectionDB';
const DB_VERSION = 1;

// Open IndexedDB connection
function openDB() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(DB_NAME, DB_VERSION);

    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve(request.result);

    request.onupgradeneeded = (event) => {
      const db = event.target.result;

      // Create object stores if they don't exist
      if (!db.objectStoreNames.contains('inspections')) {
        const inspectionsStore = db.createObjectStore('inspections', { keyPath: 'id' });
        inspectionsStore.createIndex('syncStatus', 'syncStatus', { unique: false });
        inspectionsStore.createIndex('lastModified', 'lastModified', { unique: false });
      }

      if (!db.objectStoreNames.contains('userFiles')) {
        const userFilesStore = db.createObjectStore('userFiles', { keyPath: 'id' });
        userFilesStore.createIndex('folderId', 'folderId', { unique: false });
        userFilesStore.createIndex('syncStatus', 'syncStatus', { unique: false });
      }

      if (!db.objectStoreNames.contains('pendingUploads')) {
        const pendingUploadsStore = db.createObjectStore('pendingUploads', { keyPath: 'id', autoIncrement: true });
        pendingUploadsStore.createIndex('type', 'type', { unique: false });
        pendingUploadsStore.createIndex('timestamp', 'timestamp', { unique: false });
      }

      if (!db.objectStoreNames.contains('syncQueue')) {
        const syncQueueStore = db.createObjectStore('syncQueue', { keyPath: 'id', autoIncrement: true });
        syncQueueStore.createIndex('operation', 'operation', { unique: false });
        syncQueueStore.createIndex('timestamp', 'timestamp', { unique: false });
      }
    };
  });
}

// Generic functions for IndexedDB operations
async function getAllFromStore(storeName) {
  const db = await openDB();
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([storeName], 'readonly');
    const store = transaction.objectStore(storeName);
    const request = store.getAll();

    request.onsuccess = () => resolve(request.result);
    request.onerror = () => reject(request.error);
  });
}

async function deleteFromStore(storeName, key) {
  const db = await openDB();
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([storeName], 'readwrite');
    const store = transaction.objectStore(storeName);
    const request = store.delete(key);

    request.onsuccess = () => resolve(request.result);
    request.onerror = () => reject(request.error);
  });
}

// Sync functions
async function syncInspections() {
  console.log('Background sync: Syncing inspections...');
  try {
    // Get auth token from service worker context
    const clients = await self.clients.matchAll();
    let authToken = null;

    for (const client of clients) {
      // Try to get token from client
      try {
        const token = await client.postMessage({ type: 'GET_AUTH_TOKEN' });
        if (token) {
          authToken = token;
          break;
        }
      } catch (e) {
        continue;
      }
    }

    if (!authToken) {
      console.log('No auth token available for background sync');
      return;
    }

    // Fetch inspections from server
    const response = await fetch('http://197.140.142.170/api/inspections/inspections/', {
      headers: {
        'Authorization': `Token ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const serverInspections = await response.json();
      console.log('Fetched inspections from server:', serverInspections.length);

      // Store in IndexedDB
      const db = await openDB();
      const transaction = db.transaction(['inspections'], 'readwrite');
      const store = transaction.objectStore('inspections');

      for (const inspection of serverInspections) {
        const inspectionData = {
          ...inspection,
          syncStatus: 'synced',
          lastModified: new Date().toISOString()
        };
        store.put(inspectionData);
      }

      console.log('Inspections synced successfully');
    }
  } catch (error) {
    console.error('Background sync inspections failed:', error);
  }
}

async function syncFiles() {
  console.log('Background sync: Syncing files...');
  try {
    // Get pending uploads and sync queue
    const pendingUploads = await getAllFromStore('pendingUploads');
    const syncQueue = await getAllFromStore('syncQueue');

    console.log('Pending uploads:', pendingUploads.length);
    console.log('Sync queue items:', syncQueue.length);

    // Process pending uploads and sync queue items
    // This is a simplified version - in production you'd want more robust error handling

    for (const upload of pendingUploads) {
      try {
        // Process upload
        console.log('Processing pending upload:', upload.id);
        // Remove from pending after processing
        await deleteFromStore('pendingUploads', upload.id);
      } catch (error) {
        console.error('Failed to process pending upload:', error);
      }
    }

    for (const item of syncQueue) {
      try {
        // Process sync queue item
        console.log('Processing sync queue item:', item.id);
        // Remove from queue after processing
        await deleteFromStore('syncQueue', item.id);
      } catch (error) {
        console.error('Failed to process sync queue item:', error);
      }
    }

    console.log('Files sync completed');
  } catch (error) {
    console.error('Background sync files failed:', error);
  }
}

// Listen for messages from service worker
self.addEventListener('message', (event) => {
  const { type, data } = event.data;

  if (type === 'SYNC_INSPECTIONS') {
    syncInspections();
  } else if (type === 'SYNC_FILES') {
    syncFiles();
  }
});
import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  List,
  ListItem,
  ListItemText,
  Alert
} from '@mui/material';
import api from '../services/api';

const LogoTestPage = () => {
  const [logos, setLogos] = useState([]);
  const [companies, setCompanies] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [mediaTest, setMediaTest] = useState(null);

  const fetchData = async () => {
    setLoading(true);
    setError('');
    
    try {
      // Fetch companies
      const companiesResponse = await api.get('/api/inspections/companies/');
      console.log('Companies response:', companiesResponse.data);
      setCompanies(companiesResponse.data);

      // Fetch logos
      const logosResponse = await api.get('/api/inspections/settings/logos/');
      console.log('Raw logos response:', logosResponse.data);

      const rawLogos = logosResponse.data.logos || [];

      // Process logo URLs the same way as SettingsMainPage
      const processedLogos = rawLogos.map(logo => {
        let logoUrl = logo.logo_url;
        if (logoUrl) {
          if (logoUrl.startsWith('http')) {
            logoUrl = logoUrl;
          }
          else if (logoUrl.startsWith('/media/')) {
            logoUrl = `https://e-inspection-form-3.onrender.com${logoUrl}`;
          }
          else {
            logoUrl = `https://e-inspection-form-3.onrender.com/media/${logoUrl}`;
          }
        }

        return {
          ...logo,
          logo_url: logoUrl,
          original_url: logo.logo_url // Keep original for comparison
        };
      });

      console.log('Processed logos:', processedLogos);
      setLogos(processedLogos);
      
    } catch (err) {
      console.error('Error fetching data:', err);
      setError(`Error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const testImageLoad = (url) => {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => resolve(true);
      img.onerror = () => resolve(false);
      img.src = url;
    });
  };

  const testAllLogos = async () => {
    console.log('Testing logo URLs...');
    for (const logo of logos) {
      if (logo.logo_url) {
        const canLoad = await testImageLoad(logo.logo_url);
        console.log(`Logo ${logo.id} (${logo.logo_type} for ${logo.company_name}): ${canLoad ? 'LOADS' : 'FAILS'} - ${logo.logo_url}`);
      }
    }
  };

  const testMediaAccess = async () => {
    try {
      const response = await api.get('/api/inspections/test/media/');
      console.log('Media test response:', response.data);
      setMediaTest(response.data);
    } catch (err) {
      console.error('Media test error:', err);
      setError(`Media test error: ${err.message}`);
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Logo System Debug Page
      </Typography>

      <Button 
        variant="contained" 
        onClick={fetchData} 
        disabled={loading}
        sx={{ mb: 2, mr: 2 }}
      >
        Refresh Data
      </Button>

      <Button
        variant="outlined"
        onClick={testAllLogos}
        disabled={loading || logos.length === 0}
        sx={{ mb: 2, mr: 2 }}
      >
        Test Logo URLs
      </Button>

      <Button
        variant="outlined"
        onClick={testMediaAccess}
        disabled={loading}
        sx={{ mb: 2 }}
      >
        Test Media Access
      </Button>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ p: 2, mb: 2 }}>
        <Typography variant="h6" gutterBottom>
          Companies ({companies.length})
        </Typography>
        <List dense>
          {companies.map(company => (
            <ListItem key={company.id}>
              <ListItemText 
                primary={company.name}
                secondary={`ID: ${company.id}, Created: ${company.created_at}`}
              />
            </ListItem>
          ))}
        </List>
      </Paper>

      <Paper sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          Logos ({logos.length})
        </Typography>
        <List dense>
          {logos.map(logo => (
            <ListItem key={logo.id}>
              <ListItemText 
                primary={`${logo.logo_type} for ${logo.company_name || 'No Company'}`}
                secondary={
                  <Box>
                    <Typography variant="caption" display="block">
                      ID: {logo.id}
                    </Typography>
                    <Typography variant="caption" display="block">
                      Original URL: {logo.original_url || 'No URL'}
                    </Typography>
                    <Typography variant="caption" display="block">
                      Processed URL: {logo.logo_url || 'No URL'}
                    </Typography>
                    <Typography variant="caption" display="block">
                      Uploaded: {logo.uploaded_at}
                    </Typography>
                    {logo.logo_url && (
                      <Box sx={{ mt: 1 }}>
                        <img 
                          src={logo.logo_url} 
                          alt="Logo preview"
                          style={{ 
                            maxWidth: '100px', 
                            maxHeight: '100px',
                            border: '1px solid #ccc'
                          }}
                          onLoad={() => console.log(`Image loaded: ${logo.logo_url}`)}
                          onError={() => console.log(`Image failed to load: ${logo.logo_url}`)}
                        />
                      </Box>
                    )}
                  </Box>
                }
              />
            </ListItem>
          ))}
        </List>
      </Paper>

      {mediaTest && (
        <Paper sx={{ p: 2, mt: 2 }}>
          <Typography variant="h6" gutterBottom>
            Media Test Results
          </Typography>
          <Typography variant="body2" gutterBottom>
            Media Root: {mediaTest.media_root}
          </Typography>
          <Typography variant="body2" gutterBottom>
            Media URL: {mediaTest.media_url}
          </Typography>
          <Typography variant="body2" gutterBottom>
            Debug Mode: {mediaTest.debug ? 'True' : 'False'}
          </Typography>

          <Typography variant="subtitle1" sx={{ mt: 2, mb: 1 }}>
            File System Check:
          </Typography>
          <List dense>
            {mediaTest.logos?.map(logo => (
              <ListItem key={logo.id}>
                <ListItemText
                  primary={`${logo.logo_type} for ${logo.company_name}`}
                  secondary={
                    <Box>
                      <Typography variant="caption" display="block">
                        File Path: {logo.file_path}
                      </Typography>
                      <Typography variant="caption" display="block">
                        File URL: {logo.file_url}
                      </Typography>
                      <Typography variant="caption" display="block" color={logo.file_exists ? 'success.main' : 'error.main'}>
                        File Exists: {logo.file_exists ? 'YES' : 'NO'}
                      </Typography>
                      <Typography variant="caption" display="block">
                        File Size: {logo.file_size} bytes
                      </Typography>
                    </Box>
                  }
                />
              </ListItem>
            ))}
          </List>
        </Paper>
      )}
    </Box>
  );
};

export default LogoTestPage;
